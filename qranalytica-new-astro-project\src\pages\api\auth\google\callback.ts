import { Google, OAuth2RequestError, decodeIdToken } from "arctic";
import type { APIRoute } from "astro";
import { v4 as uuidv4 } from "uuid";
import { signJwt } from "../../../../lib/jwt";

export const prerender = false;
const clientId = import.meta.env.GOOGLE_CLIENT_ID;
const clientSecret = import.meta.env.GOOGLE_CLIENT_SECRET;
const redirectUri = import.meta.env.GOOGLE_REDIRECT_URI || "http://localhost:4321/api/auth/google/callback";

const google = new Google(clientId, clientSecret, redirectUri);

export const GET: APIRoute = async ({ url, cookies, redirect, locals }) => {
  const code = url.searchParams.get("code");
  const state = url.searchParams.get("state");

  const storedState = cookies.get("oauth_state")?.value;
  const codeVerifier = cookies.get("oauth_code_verifier")?.value;

  if (!code || !state || !storedState || !codeVerifier || state !== storedState) {
    return new Response("Invalid OAuth response", { status: 400 });
  }

  try {
    const tokens = await google.validateAuthorizationCode(code, codeVerifier);

    // Decode ID token for user info (if available)
    let user: any = null;
    if (tokens.idToken()) {
      const claims: any = decodeIdToken(tokens.idToken());
      user = {
        email: claims.email,
        name: claims.name || claims.email,
        picture: claims.picture,
        sub: claims.sub,
      };
    }

    // Fallback: get profile from Google userinfo endpoint
    if (!user) {
      const resp = await fetch("https://openidconnect.googleapis.com/v1/userinfo", {
        headers: {
          Authorization: `Bearer ${tokens.accessToken()}`,
        },
      });
      if (resp.ok) {
        user = await resp.json();
      }
    }

    if (!user) {
      return new Response("Unable to fetch user profile", { status: 500 });
    }

    // Persist / upsert user into D1
    try {
      const db: D1Database = (locals as any).runtime.env.DB;
      const userId = user.sub || uuidv4();
      await db
        .prepare(
          `INSERT INTO users (id, email, name, picture) VALUES (?1, ?2, ?3, ?4)
           ON CONFLICT(email) DO UPDATE SET name=excluded.name, picture=excluded.picture`
        )
        .bind(userId, user.email, user.name, user.picture)
        .run();

      // Map custom sub id for future use
      user.id = userId;
    } catch (err) {
      console.error("D1 user upsert failed", err);
    }

    // Determine if request is https to decide secure flag (dev vs prod)
    const isSecure = url.protocol === "https:";

    // Store user in cookie
    const userJson = JSON.stringify(user);
    const encodedUser = encodeURIComponent(userJson);
    console.log("Debug: Setting user cookie - original:", userJson);
    console.log("Debug: Setting user cookie - encoded:", encodedUser);
    
    cookies.set("user", encodedUser, {
      httpOnly: false, // Accessible on client for quick checks (non-sensitive info only)
      maxAge: 60 * 60 * 24 * 7, // 7 days
      sameSite: "lax",
      secure: isSecure,
      path: "/",
    });

    // Sign JWT for server validation
    try {
      const secret: string = (locals as any).runtime.env.JWT_SECRET;
      const token = await signJwt({ sub: user.id, email: user.email }, secret);
      cookies.set("auth_token", token, {
        httpOnly: true,
        maxAge: 60 * 60 * 24 * 7,
        sameSite: "lax",
        secure: isSecure,
        path: "/",
      });
    } catch (err) {
      console.error("JWT sign failed", err);
    }

    // Clear temporary cookies
    cookies.delete("oauth_state");
    cookies.delete("oauth_code_verifier");

    return redirect("/tool/qr-code-generator", 302);
  } catch (e) {
    if (e instanceof OAuth2RequestError) {
      return new Response("Invalid authorization code", { status: 400 });
    }
    console.error("OAuth callback error", e);
    return new Response("OAuth Error", { status: 500 });
  }
}; 