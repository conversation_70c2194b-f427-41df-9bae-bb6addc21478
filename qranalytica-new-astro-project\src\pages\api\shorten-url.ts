import type { APIRoute } from "astro";
import { v4 as uuidv4 } from "uuid";
export const prerender = false;

// Generate random slug
function generateRandomSlug(length: number = 8): string {
  const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

export const POST: APIRoute = async ({ request, locals }) => {
  // @ts-ignore
  const env = locals.runtime.env;
  // @ts-ignore
  const db = env.DB as D1Database;

  if (!db) {
    return new Response(JSON.stringify({ error: "D1 database not configured." }), { status: 500 });
  }

  const payload: any = await request.json();
  const { 
    originalUrl, 
    customSlug, 
    linkName, 
    userEmail, 
    selectedDomain, 
    analyticsEnabled 
  } = payload;

  // Validate original URL
  if (!originalUrl) {
    return new Response(JSON.stringify({ error: "Original URL is required" }), { status: 400 });
  }

  try {
    new URL(originalUrl);
  } catch {
    return new Response(JSON.stringify({ error: "Invalid URL format" }), { status: 400 });
  }

  // Validate link name
  if (!linkName || !linkName.trim()) {
    return new Response(JSON.stringify({ error: "Link name is required" }), { status: 400 });
  }

  // Generate or use custom slug
  let slug = customSlug;
  
  if (!slug) {
    // Generate a random slug and ensure it's unique
    let attempts = 0;
    const maxAttempts = 10;
    
    do {
      slug = generateRandomSlug();
      attempts++;
      
      // Check if slug exists in both tables
      const qrExists = await db.prepare(
        `SELECT COUNT(*) as count FROM qr_codes WHERE custom_slug = ?1`
      ).bind(slug).first();
      
      const urlExists = await db.prepare(
        `SELECT COUNT(*) as count FROM shortened_urls WHERE slug = ?1`
      ).bind(slug).first();
      
      const qrCount = (qrExists as any)?.count || 0;
      const urlCount = (urlExists as any)?.count || 0;
      
      if (qrCount === 0 && urlCount === 0) {
        break;
      }
      
      if (attempts >= maxAttempts) {
        return new Response(JSON.stringify({ error: "Failed to generate unique slug" }), { status: 500 });
      }
    } while (true);
  } else {
    // Validate custom slug
    if (!/^[a-zA-Z0-9-_]+$/.test(slug) || slug.length < 3 || slug.length > 50) {
      return new Response(JSON.stringify({ error: "Invalid slug format" }), { status: 400 });
    }

    // Check if custom slug is available
    const qrExists = await db.prepare(
      `SELECT COUNT(*) as count FROM qr_codes WHERE custom_slug = ?1`
    ).bind(slug).first();
    
    const urlExists = await db.prepare(
      `SELECT COUNT(*) as count FROM shortened_urls WHERE slug = ?1`
    ).bind(slug).first();

    const qrCount = (qrExists as any)?.count || 0;
    const urlCount = (urlExists as any)?.count || 0;
    
    if (qrCount > 0 || urlCount > 0) {
      return new Response(JSON.stringify({ error: "Slug is already taken" }), { status: 400 });
    }
  }

  const urlId = uuidv4();
  let userId = null;

  // Get or create user if email is provided
  if (userEmail) {
    try {
      const existingUser = await db.prepare(
        `SELECT id FROM users WHERE email = ?`
      ).bind(userEmail).first();

      if (existingUser) {
        userId = existingUser.id;
      } else {
        userId = uuidv4();
        await db.prepare(
          `INSERT INTO users (id, email) VALUES (?, ?)`
        ).bind(userId, userEmail).run();
      }
    } catch (userErr) {
      console.warn("Failed to handle user:", userErr);
    }
  }

  // Set default domain if not specified
  const domain = selectedDomain || "qranalytica.com";

  try {
    // Insert the shortened URL with enhanced data
    await db.prepare(
      `INSERT INTO shortened_urls (
        id, user_id, name, original_url, slug, domain, analytics_enabled, 
        clicks, unique_clicks, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, 0, 0, datetime('now'), datetime('now'))`
    ).bind(
      urlId, 
      userId, 
      linkName.trim(), 
      originalUrl, 
      slug, 
      domain,
      analyticsEnabled ? 1 : 0
    ).run();

    return new Response(JSON.stringify({
      id: urlId,
      originalUrl,
      slug,
      linkName: linkName.trim(),
      domain,
      shortUrl: `https://${domain}/${slug}`,
      analyticsEnabled,
      success: true
    }), {
      headers: { "Content-Type": "application/json" },
      status: 201,
    });
  } catch (err) {
    console.error("Database error:", err);
    const errorMessage = err instanceof Error ? err.message : 'Unknown error';
    return new Response(JSON.stringify({ 
      error: "Failed to create shortened URL", 
      details: errorMessage 
    }), { status: 500 });
  }
}; 