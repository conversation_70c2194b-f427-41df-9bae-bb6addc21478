-- Users table
CREATE TABLE IF NOT EXISTS users (
  id TEXT PRIMARY KEY,
  email TEXT NOT NULL UNIQUE,
  name TEXT,
  picture TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Custom domains table
CREATE TABLE IF NOT EXISTS custom_domains (
  id TEXT PRIMARY KEY,
  user_id TEXT NOT NULL,
  domain TEXT NOT NULL UNIQUE,
  verified INTEGER DEFAULT 0,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- QR codes table
CREATE TABLE IF NOT EXISTS qr_codes (
  id TEXT PRIMARY KEY,
  user_id TEXT,
  name TEXT,
  data TEXT NOT NULL,
  dynamic INTEGER DEFAULT 0,
  tracking_domain TEXT,
  custom_slug TEXT,
  redirect_url TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREI<PERSON>N KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- QR code scan analytics table
CREATE TABLE IF NOT EXISTS qr_code_scan_analytics (
  id TEXT PRIMARY KEY,
  qr_code_id TEXT NOT NULL,
  scan_time DATETIME DEFAULT CURRENT_TIMESTAMP,
  ip TEXT,
  user_agent TEXT,
  referrer TEXT,
  lat REAL,
  lon REAL,
  city TEXT,
  country TEXT,
  device TEXT,
  os TEXT,
  browser TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (qr_code_id) REFERENCES qr_codes(id) ON DELETE CASCADE
);

-- Index for faster lookups on scans per code
CREATE INDEX IF NOT EXISTS idx_scan_qr_code_id ON qr_code_scan_analytics (qr_code_id); 