import React, { useState } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react';
import type { PaginationInfo } from '../../types/dashboard';

interface TablePaginationProps {
  pagination: PaginationInfo;
  onPageChange: (page: number) => void;
  onPageSizeChange?: (pageSize: number) => void;
  pageSizeOptions?: number[];
  itemName?: string; // e.g., "QR codes", "entries", etc.
}

export const TablePagination: React.FC<TablePaginationProps> = ({
  pagination,
  onPageChange,
  onPageSizeChange,
  pageSizeOptions = [5, 10, 20, 50, 100],
  itemName = "QR codes"
}) => {
  const { currentPage, totalPages, totalItems, itemsPerPage } = pagination;
  const [jumpToPage, setJumpToPage] = useState<string>('');

  const handlePageJump = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      const pageNum = parseInt(jumpToPage);
      if (pageNum >= 1 && pageNum <= totalPages) {
        onPageChange(pageNum);
        setJumpToPage('');
      }
    }
  };

  // Calculate the range of items being displayed
  const startItem = totalItems === 0 ? 0 : (currentPage - 1) * itemsPerPage + 1;
  const endItem = Math.min(currentPage * itemsPerPage, totalItems);

  // Handle edge cases
  if (totalItems === 0) {
    return ( null)
  }



  return (
    <div className="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-4 px-4 py-6 border-t border-gray-200 bg-gray-50/30">
      {/* Left side - Info and page size selector */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3">
          <div className="flex items-center space-x-1">
            <p className="text-sm text-gray-700">
              Showing <span className="font-semibold text-blue-600">{startItem}</span> to{' '}
              <span className="font-semibold text-blue-600">{endItem}</span> of{' '}
              <span className="font-semibold text-blue-600">{totalItems}</span> {itemName.toLowerCase()}
            </p>
          </div>

          {onPageSizeChange && (
            <div className="flex items-center space-x-2   rounded-lg px-3 py-1 border border-gray-200">
              <p className="text-sm text-gray-600">Show:</p>
              <Select
                value={itemsPerPage.toString()}
                onValueChange={(value) => onPageSizeChange(parseInt(value))}
                
              >
                <SelectTrigger className="h-7 w-full text-sm border-0 shadow-none focus:ring-0 p-0">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className='w-full'>
                  {pageSizeOptions.map((size) => (
                    <SelectItem key={size} value={size.toString()}>
                      {size}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-sm text-gray-600">per page</p>
            </div>
          )}
        </div>
      </div>

      {/* Right side - Navigation controls */}
      <div className="flex items-center justify-center lg:justify-end w-full lg:w-auto">
        {totalPages > 1 && (
          <div className="flex items-center space-x-1 bg-white rounded-lg border border-gray-200 p-1">
            {/* First page button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onPageChange(1)}
              disabled={currentPage === 1}
              className="h-8 w-8 p-0 hover:bg-gray-100 disabled:opacity-50"
              title="First page"
            >
              <ChevronsLeft className="h-4 w-4" />
            </Button>

            {/* Previous page button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onPageChange(currentPage - 1)}
              disabled={currentPage === 1}
              className="h-8 w-8 p-0 hover:bg-gray-100 disabled:opacity-50"
              title="Previous page"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>

            {/* Page info and jump input */}
            <div className="flex items-center space-x-2 mx-3 px-2">
              <span className="text-sm text-gray-600">Page</span>
              {totalPages > 10 ? (
                <Input
                  type="number"
                  min="1"
                  max={totalPages}
                  value={jumpToPage}
                  onChange={(e) => setJumpToPage(e.target.value)}
                  onKeyDown={handlePageJump}
                  placeholder={currentPage.toString()}
                  className="h-7 w-14 text-center text-sm border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                  title="Press Enter to jump to page"
                />
              ) : (
                <span className="font-semibold text-blue-600 min-w-[1rem] text-center">{currentPage}</span>
              )}
              <span className="text-sm text-gray-600">
                of <span className="font-semibold text-gray-900">{totalPages}</span>
              </span>
            </div>

            {/* Next page button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onPageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
              className="h-8 w-8 p-0 hover:bg-gray-100 disabled:opacity-50"
              title="Next page"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>

            {/* Last page button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onPageChange(totalPages)}
              disabled={currentPage === totalPages}
              className="h-8 w-8 p-0 hover:bg-gray-100 disabled:opacity-50"
              title="Last page"
            >
              <ChevronsRight className="h-4 w-4" />
            </Button>
          </div>
        )}
      </div>

    </div>
  );
};

export default TablePagination;
