---
import DashboardLayout from '../../../layouts/DashboardLayout.astro';
import QRAnalyticsDetailWrapper from '../../../components/dashboard/QRAnalyticsDetailWrapper';

export const prerender = false;

// Using dynamic routing - no need for getStaticPaths
// The page will be generated on-demand for any QR code ID

const { qrid } = Astro.params;
---

<DashboardLayout title={`QR Analytics - ${qrid || 'Unknown'} - QRAnalytica`}>
  <QRAnalyticsDetailWrapper qrCodeId={qrid || ''} client:load />
</DashboardLayout>
