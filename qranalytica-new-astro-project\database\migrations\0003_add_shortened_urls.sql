-- Add shortened URLs functionality
-- Migration: 0003_add_shortened_urls.sql

-- Create shortened_urls table
CREATE TABLE IF NOT EXISTS shortened_urls (
  id TEXT PRIMARY KEY,
  user_id TEXT,
  name TEXT NOT NULL,
  original_url TEXT NOT NULL,
  slug TEXT NOT NULL UNIQUE,
  domain TEXT NOT NULL DEFAULT 'qranalytica.com',
  analytics_enabled INTEGER DEFAULT 1,
  clicks INTEGER DEFAULT 0,
  unique_clicks INTEGER DEFAULT 0,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Create url_click_analytics table for detailed analytics
CREATE TABLE IF NOT EXISTS url_click_analytics (
  id TEXT PRIMARY KEY,
  url_id TEXT NOT NULL,
  ip_address TEXT,
  user_agent TEXT,
  referrer TEXT,
  device_type TEXT,
  browser TEXT,
  os TEXT,
  is_unique_click INTEGER DEFAULT 0,
  click_time DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (url_id) REFERENCES shortened_urls(id) ON DELETE CASCADE
);

-- Create indexes for faster lookups
CREATE INDEX IF NOT EXISTS idx_shortened_urls_slug ON shortened_urls (slug);
CREATE INDEX IF NOT EXISTS idx_shortened_urls_user_id ON shortened_urls (user_id);
CREATE INDEX IF NOT EXISTS idx_shortened_urls_domain ON shortened_urls (domain);
CREATE INDEX IF NOT EXISTS idx_url_click_analytics_url_id ON url_click_analytics (url_id);
CREATE INDEX IF NOT EXISTS idx_url_click_analytics_ip_time ON url_click_analytics (url_id, ip_address, click_time);

-- Create triggers to handle updated_at timestamp for shortened_urls
CREATE TRIGGER IF NOT EXISTS update_shortened_urls_updated_at 
    AFTER UPDATE ON shortened_urls
    FOR EACH ROW
    BEGIN
        UPDATE shortened_urls SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

-- Create trigger to set updated_at on insert if not provided
CREATE TRIGGER IF NOT EXISTS insert_shortened_urls_updated_at 
    AFTER INSERT ON shortened_urls
    FOR EACH ROW
    WHEN NEW.updated_at IS NULL
    BEGIN
        UPDATE shortened_urls SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END; 