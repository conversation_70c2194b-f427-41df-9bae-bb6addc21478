import React, { useState, useEffect } from 'react';
import { useAuthStore } from '../stores/authStore';
import { 
  QrCode, 
  Search, 
  Filter, 
  MoreVertical, 
  Eye, 
  Edit, 
  Trash2, 
  ExternalLink, 
  Plus,
  Calendar,
  Clock,
  Download
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Badge } from './ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from './ui/table';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from './ui/dropdown-menu';

interface QRCodeData {
  id: string;
  name: string;
  url: string;
  shortUrl: string;
  totalScans: number;
  uniqueUsers: number;
  createdAt: string;
  lastScanned: string;
  status: 'active' | 'paused' | 'expired';
  qrType: string;
}

// Removed mock data - all data now comes from database

interface QRCodesListProps {
  onViewAnalytics?: (id: string) => void;
  onEditQR?: (qr: QRCodeData) => void;
  onCreateQR?: () => void;
}

export function QRCodesList({ onViewAnalytics, onEditQR, onCreateQR }: QRCodesListProps) {
  const { session } = useAuthStore();
  const [qrCodes, setQrCodes] = useState<QRCodeData[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(true);

  // Fetch QR codes list
  useEffect(() => {
    const fetchQrCodes = async () => {
      if (!session?.user?.email) {
        setIsLoading(false);
        return;
      }

      try {
        const res = await fetch('/api/get-qr-list', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            type: 'getQRList',
            email: session.user.email,
            page: 1,
            limit: 100,
          }),
        });

        if (!res.ok) throw new Error('Failed to fetch QR list');
        const json: any = await res.json();

        if (json.success && Array.isArray(json.data)) {
          const mapped: QRCodeData[] = json.data.map((qr: any): QRCodeData => ({
            id: qr.id?.toString() ?? '',
            name: qr.name ?? 'Unnamed QR',
            url: qr.original_url ?? '',
            shortUrl: qr.custom_slug ? `qr.qranalytica.com/${qr.custom_slug}` : qr.original_url ?? '',
            totalScans: Number(qr.total_scans ?? 0),
            uniqueUsers: Number(qr.unique_visitors ?? 0),
            createdAt: qr.created_at ?? '',
            lastScanned: qr.last_scanned ?? '',
            status: qr.dynamic ? 'active' : 'active',
            qrType: qr.content_type ?? 'URL',
          }));
          setQrCodes(mapped);
        } else {
          console.error('Failed to load QR codes: Invalid response format');
          setQrCodes([]);
        }
      } catch (error) {
        console.error('Error loading QR codes', error);
        setQrCodes([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchQrCodes();
  }, [session?.user?.email]);

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(num);
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleDateString();
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      active: 'default',
      paused: 'secondary',
      expired: 'destructive',
    } as const;
    return <Badge variant={variants[status as keyof typeof variants] || 'default'}>{status}</Badge>;
  };

  const handleDeleteQR = async (id: string) => {
    if (!confirm('Are you sure you want to delete this QR code?')) return;
    
    try {
      // Call delete API
      const res = await fetch('/api/qr-codes', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ id }),
      });

      if (res.ok) {
        setQrCodes(prev => prev.filter(qr => qr.id !== id));
      }
    } catch (error) {
      console.error('Error deleting QR code:', error);
    }
  };

  const filteredQRCodes = qrCodes.filter(qr => 
    qr.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    qr.url.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2 mb-8"></div>
          <div className="space-y-4">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-[#2C3E50]">QR Codes</h1>
          <p className="text-muted-foreground mt-1">
            Manage and monitor your QR code campaigns
          </p>
        </div>
        <Button onClick={onCreateQR} className="bg-[#18BC9C] hover:bg-[#18BC9C]/90">
          <Plus className="w-4 h-4 mr-2" />
          Create QR Code
        </Button>
      </div>

      {/* Search and Filters */}
      <div className="flex gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
          <Input
            placeholder="Search QR codes..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        <Button variant="outline">
          <Filter className="w-4 h-4 mr-2" />
          Filter
        </Button>
      </div>

      {/* QR Codes Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <QrCode className="w-5 h-5" />
            Your QR Codes ({filteredQRCodes.length})
          </CardTitle>
          <CardDescription>
            Track performance and manage your QR code campaigns
          </CardDescription>
        </CardHeader>
        <CardContent>
          {filteredQRCodes.length === 0 ? (
            <div className="text-center py-12">
              <QrCode className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No QR codes found</h3>
              <p className="text-muted-foreground mb-4">
                {searchQuery ? 'No QR codes match your search.' : 'Create your first QR code to get started.'}
              </p>
              {!searchQuery && (
                <Button onClick={onCreateQR} className="bg-[#18BC9C] hover:bg-[#18BC9C]/90">
                  <Plus className="w-4 h-4 mr-2" />
                  Create QR Code
                </Button>
              )}
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Scans</TableHead>
                  <TableHead>Unique Users</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredQRCodes.map((qr) => (
                  <TableRow key={qr.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{qr.name}</div>
                        <div className="text-sm text-muted-foreground truncate max-w-xs">
                          {qr.shortUrl}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">{qr.qrType}</Badge>
                    </TableCell>
                    <TableCell className="font-medium">
                      {formatNumber(qr.totalScans)}
                    </TableCell>
                    <TableCell>
                      {formatNumber(qr.uniqueUsers)}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1 text-sm text-muted-foreground">
                        <Calendar className="w-3 h-3" />
                        {formatDate(qr.createdAt)}
                      </div>
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(qr.status)}
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => onViewAnalytics?.(qr.id)}>
                            <Eye className="mr-2 h-4 w-4" />
                            View Analytics
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => onEditQR?.(qr)}>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => window.open(qr.url, '_blank')}>
                            <ExternalLink className="mr-2 h-4 w-4" />
                            Visit URL
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Download className="mr-2 h-4 w-4" />
                            Download QR
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            onClick={() => handleDeleteQR(qr.id)}
                            className="text-destructive"
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
} 