import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '../ui/card';
import { QrC<PERSON>, BarChart3, TrendingUp, TrendingDown, Eye, Activity } from 'lucide-react';
import type { DashboardMetrics } from '../../types/dashboard';

interface MetricsCardsProps {
  metrics: DashboardMetrics;
}

interface MetricCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  trend?: {
    value: number;
    label: string;
  };
  description?: string;
  gradient: string;
  iconBg: string;
  delay?: number;
}

const AnimatedCounter: React.FC<{ value: number; duration?: number }> = ({ value, duration = 2000 }) => {
  const [count, setCount] = useState(0);

  useEffect(() => {
    let startTime: number;
    let animationFrame: number;

    const animate = (timestamp: number) => {
      if (!startTime) startTime = timestamp;
      const progress = Math.min((timestamp - startTime) / duration, 1);

      setCount(Math.floor(progress * value));

      if (progress < 1) {
        animationFrame = requestAnimationFrame(animate);
      }
    };

    animationFrame = requestAnimationFrame(animate);

    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }
    };
  }, [value, duration]);

  return <span>{count.toLocaleString()}</span>;
};

const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  icon,
  trend,
  description,
  gradient,
  iconBg,
  delay = 0
}) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, delay);

    return () => clearTimeout(timer);
  }, [delay]);

  const numericValue = typeof value === 'number' ? value : parseInt(value.toString().replace(/,/g, '')) || 0;
  const isPositiveTrend = trend && trend.value > 0;
  const isNegativeTrend = trend && trend.value < 0;

  return (
    <Card className={`group relative overflow-hidden border-0 shadow-lg hover:shadow-xl transition-all duration-500 transform ${
      isVisible ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0'
    } hover:-translate-y-1`}>
      {/* Gradient Background */}
      <div className={`absolute inset-0 bg-gradient-to-br ${gradient} opacity-5 group-hover:opacity-10 transition-opacity duration-300`} />

      {/* Animated Border */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000" />

      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3 relative z-10">
        <CardTitle className="text-sm font-semibold text-gray-600 group-hover:text-gray-800 transition-colors">
          {title}
        </CardTitle>
        <div className={`h-12 w-12 rounded-xl ${iconBg} flex items-center justify-center text-white shadow-lg group-hover:scale-110 transition-transform duration-300`}>
          <div className="h-6 w-6">
            {icon}
          </div>
        </div>
      </CardHeader>

      <CardContent className="relative z-10">
        <div className="text-3xl font-bold text-gray-900 mb-2 group-hover:text-gray-800 transition-colors">
          {isVisible ? <AnimatedCounter value={numericValue} /> : '0'}
        </div>

        {trend && (
          <div className={`flex items-center space-x-2 text-sm mb-2 ${
            isPositiveTrend ? 'text-emerald-600' : isNegativeTrend ? 'text-red-500' : 'text-gray-500'
          }`}>
            <div className={`flex items-center space-x-1 px-2 py-1 rounded-full ${
              isPositiveTrend ? 'bg-emerald-50' : isNegativeTrend ? 'bg-red-50' : 'bg-gray-50'
            }`}>
              {isPositiveTrend ? (
                <TrendingUp className="h-3 w-3" />
              ) : isNegativeTrend ? (
                <TrendingDown className="h-3 w-3" />
              ) : (
                <Activity className="h-3 w-3" />
              )}
              <span className="font-semibold">
                {isPositiveTrend ? '+' : ''}{trend.value}%
              </span>
            </div>
            <span className="text-gray-500 text-xs">{trend.label}</span>
          </div>
        )}

        {description && (
          <p className="text-xs text-gray-500 leading-relaxed group-hover:text-gray-600 transition-colors">
            {description}
          </p>
        )}

        {/* Subtle pulse animation for active metrics */}
        <div className="absolute top-4 right-4 w-2 h-2 bg-green-400 rounded-full animate-pulse opacity-60" />
      </CardContent>
    </Card>
  );
};

export const MetricsCards: React.FC<MetricsCardsProps> = ({ metrics }) => {
  const cards = [
    {
      title: 'Total Active QR Codes',
      value: metrics.totalActiveQRCodes,
      icon: <QrCode className="h-6 w-6" />,
      description: 'Currently active QR codes',
      gradient: 'from-blue-500 to-blue-600',
      iconBg: 'bg-gradient-to-br from-blue-500 to-blue-600',
      delay: 0
    },
    {
      title: 'Total Scan Count',
      value: metrics.totalScanCount,
      icon: <BarChart3 className="h-6 w-6" />,
      trend: metrics.monthlyGrowth ? {
        value: metrics.monthlyGrowth,
        label: 'from last month'
      } : undefined,
      description: 'All-time total scans',
      gradient: 'from-emerald-500 to-emerald-600',
      iconBg: 'bg-gradient-to-br from-emerald-500 to-emerald-600',
      delay: 200
    },
    {
      title: "Today's Scans",
      value: metrics.todayScanCount,
      icon: <Eye className="h-6 w-6" />,
      trend: metrics.weeklyGrowth ? {
        value: metrics.weeklyGrowth,
        label: 'from last week'
      } : undefined,
      description: 'Scans recorded today',
      gradient: 'from-purple-500 to-purple-600',
      iconBg: 'bg-gradient-to-br from-purple-500 to-purple-600',
      delay: 400
    }
  ];

  return (
    <div className="grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
      {cards.map((card, index) => (
        <MetricCard
          key={index}
          title={card.title}
          value={card.value}
          icon={card.icon}
          trend={card.trend}
          description={card.description}
          gradient={card.gradient}
          iconBg={card.iconBg}
          delay={card.delay}
        />
      ))}
    </div>
  );
};

export default MetricsCards;
