# Custom Domains Admin Page

This document describes the custom domains management feature for QRAnalytica, which allows users to add and manage custom domains for their Cloudflare Pages projects.

## Features

### 🌐 Domain Management
- **Add Custom Domains**: Users can add custom domains to point to their Cloudflare Pages projects
- **Domain Verification**: DNS-based verification using TXT records
- **Status Tracking**: Real-time status monitoring (pending, active, failed, expired)
- **SSL Management**: Automatic SSL certificate provisioning and monitoring

### ⚡ Cloudflare Integration
- **Automatic DNS Setup**: Seamless integration with Cloudflare API for DNS record management
- **Pages Project Linking**: Direct integration with Cloudflare Pages projects
- **Zone Management**: Automatic detection and configuration of Cloudflare zones
- **API-Driven**: Full API support for programmatic domain management

### 🎨 Professional UI
- **shadcn/ui Components**: Modern, responsive interface using shadcn/ui component library
- **Real-time Updates**: Live status updates and notifications
- **Copy-to-Clipboard**: Easy copying of DNS records and verification tokens
- **Responsive Design**: Mobile-friendly interface with proper responsive behavior

## File Structure

```
src/
├── pages/
│   ├── dashboard/
│   │   └── custom-domains.astro          # Main admin page
│   └── api/
│       ├── custom-domains.ts             # CRUD operations API
│       └── custom-domains/
│           ├── verify.ts                 # Domain verification API
│           └── cloudflare.ts             # Cloudflare integration API
├── components/
│   └── dashboard/
│       └── CustomDomainsManager.tsx      # Main React component
└── lib/
    └── cloudflare-api.ts                 # Cloudflare API service

database/
└── migrations/
    └── 0004_enhance_custom_domains.sql   # Database schema enhancement
```

## Database Schema

The enhanced `custom_domains` table includes:

```sql
CREATE TABLE custom_domains (
  id TEXT PRIMARY KEY,
  user_id TEXT NOT NULL,
  domain TEXT NOT NULL UNIQUE,
  status TEXT DEFAULT 'pending',           -- pending, active, failed, expired
  ssl_status TEXT DEFAULT 'pending',       -- pending, active, failed
  verification_method TEXT DEFAULT 'dns',  -- dns, http
  verification_token TEXT,                 -- Token for domain verification
  dns_records TEXT,                        -- JSON field for required DNS records
  cloudflare_zone_id TEXT,                 -- Cloudflare zone ID if managed by CF
  cloudflare_custom_hostname_id TEXT,      -- CF custom hostname ID
  pages_project_name TEXT,                 -- Associated Pages project name
  verified INTEGER DEFAULT 0,
  last_verified_at DATETIME,               -- Last successful verification
  expires_at DATETIME,                     -- SSL certificate expiration
  error_message TEXT,                      -- Error details if verification fails
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

## API Endpoints

### 1. Domain Management (`/api/custom-domains`)

#### GET - List Domains
```bash
GET /api/custom-domains?user_id=default-user
```

#### POST - Add Domain
```bash
POST /api/custom-domains
Content-Type: application/json

{
  "domain": "example.com",
  "pages_project_name": "my-project",
  "user_id": "default-user"
}
```

#### DELETE - Remove Domain
```bash
DELETE /api/custom-domains?id=domain-id&user_id=default-user
```

### 2. Domain Verification (`/api/custom-domains/verify`)

#### POST - Verify Domain
```bash
POST /api/custom-domains/verify
Content-Type: application/json

{
  "domain_id": "domain-id",
  "user_id": "default-user"
}
```

#### GET - Check Verification Status
```bash
GET /api/custom-domains/verify?domain_id=domain-id&user_id=default-user
```

### 3. Cloudflare Integration (`/api/custom-domains/cloudflare`)

#### POST - Setup/Remove Cloudflare
```bash
POST /api/custom-domains/cloudflare
Content-Type: application/json

{
  "domain_id": "domain-id",
  "action": "setup", // or "remove"
  "user_id": "default-user"
}
```

#### GET - Check Cloudflare Status
```bash
GET /api/custom-domains/cloudflare?domain_id=domain-id&user_id=default-user
```

## Environment Variables

To enable full Cloudflare integration, set these environment variables:

```bash
# Cloudflare API credentials
CLOUDFLARE_API_TOKEN=your-api-token
CLOUDFLARE_ACCOUNT_ID=your-account-id
```

## Usage Flow

### 1. Adding a Domain
1. User clicks "Add Domain" button
2. Enters domain name and optional Pages project name
3. System generates verification token and DNS records
4. Domain is created with "pending" status

### 2. Domain Verification
1. User adds required DNS records to their domain
2. User clicks "Verify" button
3. System checks DNS records for verification token
4. Domain status updates to "active" if verification succeeds

### 3. Cloudflare Integration
1. For verified domains, user can click "Setup CF" button
2. System uses Cloudflare API to configure DNS records
3. CNAME record is created pointing to Pages project
4. Domain is automatically configured for Cloudflare Pages

### 4. Domain Management
1. Users can view all domains with their status
2. Copy DNS records for manual configuration
3. Remove domains from Cloudflare or delete entirely
4. Monitor SSL certificate status and expiration

## Security Considerations

- **Domain Ownership Verification**: DNS-based verification ensures domain ownership
- **API Authentication**: All API endpoints require user authentication
- **Input Validation**: Domain names are validated using regex patterns
- **Error Handling**: Comprehensive error handling with user-friendly messages

## Future Enhancements

- **Bulk Domain Import**: Support for importing multiple domains
- **Custom SSL Certificates**: Support for custom SSL certificate uploads
- **Domain Analytics**: Traffic and performance analytics per domain
- **Automated Renewal**: Automatic SSL certificate renewal notifications
- **DNS Health Monitoring**: Continuous monitoring of DNS record health

## Troubleshooting

### Common Issues

1. **Domain Verification Fails**
   - Check DNS propagation (can take up to 48 hours)
   - Verify TXT record is correctly added
   - Ensure no conflicting DNS records exist

2. **Cloudflare Setup Fails**
   - Verify API credentials are correct
   - Ensure domain is added to Cloudflare account
   - Check account permissions for API token

3. **SSL Certificate Issues**
   - Wait for certificate provisioning (can take 15 minutes)
   - Verify domain is accessible via HTTP
   - Check for CAA records blocking certificate issuance

### Debug Mode

Enable debug logging by setting:
```bash
DEBUG=true
```

This will provide detailed logs for API calls and verification processes.
