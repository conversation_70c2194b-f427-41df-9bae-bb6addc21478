import type { APIRoute } from "astro";
import { v4 as uuidv4 } from "uuid";
export const prerender = false;

// Helper function to parse user agent
function parseUserAgent(userAgent: string) {
  const deviceType = /Mobile|Android|iPhone|iPad/.test(userAgent) ? "Mobile" : "Desktop";
  
  let browser = "Unknown";
  if (userAgent.includes("Chrome")) browser = "Chrome";
  else if (userAgent.includes("Firefox")) browser = "Firefox";
  else if (userAgent.includes("Safari")) browser = "Safari";
  else if (userAgent.includes("Edge")) browser = "Edge";
  
  let os = "Unknown";
  if (userAgent.includes("Windows")) os = "Windows";
  else if (userAgent.includes("Mac")) os = "macOS";
  else if (userAgent.includes("Android")) os = "Android";
  else if (userAgent.includes("iPhone") || userAgent.includes("iPad")) os = "iOS";
  else if (userAgent.includes("Linux")) os = "Linux";
  
  return { deviceType, browser, os };
}

// Helper function to get IP address
function getClientIP(request: Request): string {
  const headers = request.headers;
  return headers.get("cf-connecting-ip") || 
         headers.get("x-forwarded-for")?.split(",")[0] || 
         headers.get("x-real-ip") || 
         "unknown";
}

export const GET: APIRoute = async ({ params, locals, redirect, request }) => {
  // @ts-ignore
  const env = locals.runtime.env;
  // @ts-ignore
  const db = env.DB as D1Database;

  if (!db) {
    return new Response("Database not configured", { status: 500 });
  }

  const slug = params.slug;

  if (!slug) {
    return redirect("/", 302);
  }

  try {
    // Check shortened_urls table first
    const shortUrl = await db.prepare(
      `SELECT id, original_url, analytics_enabled FROM shortened_urls WHERE slug = ?`
    ).bind(slug).first();

    if (shortUrl) {
      // Check if this is a unique visitor (simplified - based on IP in last 24 hours)
      const clientIP = getClientIP(request);
      const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();
      
      const existingClick = await db.prepare(
        `SELECT COUNT(*) as count FROM url_click_analytics 
         WHERE url_id = ? AND ip_address = ? AND click_time > ?`
      ).bind(shortUrl.id, clientIP, twentyFourHoursAgo).first();
      
      const isUniqueClick = (existingClick as any)?.count === 0;

      // Update click counts
      if (isUniqueClick) {
        await db.prepare(
          `UPDATE shortened_urls 
           SET clicks = clicks + 1, unique_clicks = unique_clicks + 1, updated_at = CURRENT_TIMESTAMP 
           WHERE slug = ?`
        ).bind(slug).run();
      } else {
        await db.prepare(
          `UPDATE shortened_urls 
           SET clicks = clicks + 1, updated_at = CURRENT_TIMESTAMP 
           WHERE slug = ?`
        ).bind(slug).run();
      }

      // Record detailed analytics if enabled
      if (shortUrl.analytics_enabled === 1) {
        const userAgent = request.headers.get("user-agent") || "";
        const referrer = request.headers.get("referer") || "";
        const { deviceType, browser, os } = parseUserAgent(userAgent);

        // Insert click analytics
        await db.prepare(
          `INSERT INTO url_click_analytics (
            id, url_id, ip_address, user_agent, referrer, 
            device_type, browser, os, is_unique_click
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`
        ).bind(
          uuidv4(),
          shortUrl.id,
          clientIP,
          userAgent,
          referrer,
          deviceType,
          browser,
          os,
          isUniqueClick ? 1 : 0
        ).run();

        // TODO: Add geolocation lookup for country/city
        // This would typically use a service like MaxMind GeoIP or similar
      }

      // Redirect to original URL
      return redirect(shortUrl.original_url as string, 301);
    }

    // Check QR codes table as fallback
    const qrCode = await db.prepare(
      `SELECT redirect_url FROM qr_codes WHERE custom_slug = ?`
    ).bind(slug).first();

    if (qrCode && qrCode.redirect_url) {
      return redirect(qrCode.redirect_url as string, 301);
    }

    // If no match found, redirect to 404 or homepage
    return redirect("/", 302);

  } catch (error) {
    console.error("Error in redirect:", error);
    return redirect("/", 302);
  }
}; 