import { Google, generateState, generateCodeVerifier } from "arctic";
import type { APIRoute } from "astro";
export const prerender = false;
// Ensure environment variables are available
const clientId = import.meta.env.GOOGLE_CLIENT_ID;
const clientSecret = import.meta.env.GOOGLE_CLIENT_SECRET;
const redirectUri = import.meta.env.GOOGLE_REDIRECT_URI || "http://localhost:4321/api/auth/google/callback";

if (!clientId || !clientSecret) {
  throw new Error("Google OAuth credentials are missing from environment variables");
}

const google = new Google(clientId, clientSecret, redirectUri);

export const GET: APIRoute = async ({ request, cookies, redirect }) => {
  // Generate state & PKCE code verifier
  const state = generateState();
  const codeVerifier = generateCodeVerifier();
  const scopes = ["openid", "profile", "email"];

  // Create Google authorization URL
  const url = google.createAuthorizationURL(state, codeVerifier, scopes);

  const isSecure = new URL(request.url).protocol === "https:";
  // Store state & verifier in cookies (short-lived)
  const maxAge = 60 * 10; // 10 minutes
  cookies.set("oauth_state", state, {
    httpOnly: true,
    maxAge,
    sameSite: "lax",
    secure: isSecure,
    path: "/",
  });

  cookies.set("oauth_code_verifier", codeVerifier, {
    httpOnly: true,
    maxAge,
    sameSite: "lax",
    secure: isSecure,
    path: "/",
  });

  // Redirect the user to Google consent screen
  return redirect(url.toString(), 302);
}; 