import { QRCode, ScanAnalytics, Env } from './types';

/**
 * Get QR code by ID from the database
 */
export async function getQRCodeById(db: D1Database, qrCodeId: string): Promise<QRCode | null> {
  try {
    const result = await db
      .prepare('SELECT * FROM qr_codes WHERE id = ?')
      .bind(qrCodeId)
      .first<QRCode>();
    console.log("🚀 ~ getQRCodeById ~ result:", result)
    
    return result || null;
  } catch (error) {
    console.error('Error fetching QR code:', error);
    return null;
  }
}

/**
 * Get QR code by custom slug from the database
 */
export async function getQRCodeBySlug(db: D1Database, slug: string): Promise<QRCode | null> {
  try {
    const result = await db
      .prepare('SELECT * FROM qr_codes WHERE custom_slug = ?')
      .bind(slug)
      .first<QRCode>();
    
    return result || null;
  } catch (error) {
    console.error('Error fetching QR code by slug:', error);
    return null;
  }
}

/**
 * Store scan analytics data in the database
 */
export async function storeScanAnalytics(
  db: D1Database, 
  analytics: ScanAnalytics
): Promise<boolean> {
  try {
    const result = await db
      .prepare(`
        INSERT INTO qr_code_scan_analytics (
          id, qr_code_id, scan_time, ip, user_agent, referrer,
          lat, lon, city, country, device, os, browser, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `)
      .bind(
        analytics.id,
        analytics.qr_code_id,
        analytics.scan_time,
        analytics.ip,
        analytics.user_agent,
        analytics.referrer,
        analytics.lat,
        analytics.lon,
        analytics.city,
        analytics.country,
        analytics.device,
        analytics.os,
        analytics.browser,
        analytics.created_at
      )
      .run();
    
    return result.success;
  } catch (error) {
    console.error('Error storing scan analytics:', error);
    return false;
  }
}

/**
 * Get analytics data for a specific QR code (for testing/debugging)
 */
export async function getQRCodeAnalytics(
  db: D1Database, 
  qrCodeId: string, 
  limit: number = 100
): Promise<ScanAnalytics[]> {
  try {
    const result = await db
      .prepare(`
        SELECT * FROM qr_code_scan_analytics 
        WHERE qr_code_id = ? 
        ORDER BY scan_time DESC 
        LIMIT ?
      `)
      .bind(qrCodeId, limit)
      .all<ScanAnalytics>();
    
    return result.results || [];
  } catch (error) {
    console.error('Error fetching QR code analytics:', error);
    return [];
  }
}

/**
 * Health check for database connection
 */
export async function checkDatabaseHealth(db: D1Database): Promise<boolean> {
  try {
    const result = await db
      .prepare('SELECT 1 as health_check')
      .first();
    
    return result?.health_check === 1;
  } catch (error) {
    console.error('Database health check failed:', error);
    return false;
  }
}
