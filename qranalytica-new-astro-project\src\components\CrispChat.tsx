"use client"

import { useEffect } from "react";
import { Crisp } from "crisp-sdk-web";
import { useAuthStore } from "../stores/authStore";

const CrispChat = () => {
  const { session, status, init } = useAuthStore();

  useEffect(() => {
    // Initialize auth store
    init();
  }, [init]);

  useEffect(() => {
    // Configure Crisp
    Crisp.configure("63e59996-daab-43da-8a7d-b4ad89b2b628");
  }, []);

  useEffect(() => {
    // Set user data in Crisp when authenticated
    if (status === "authenticated" && session?.user) {
      const user = session.user;

      // Set user email
      if (user.email) {
        Crisp.user.setEmail(user.email);
      }

      // Set user name/nickname
      if (user.name) {
        Crisp.user.setNickname(user.name);
      }

      // Set user avatar if available
      if (user.picture) {
        Crisp.user.setAvatar(user.picture);
      }
    }
  }, [status, session]);

  return null;
}

export default CrispChat;
