import type { APIRoute } from 'astro';
import { v4 as uuidv4 } from 'uuid';

export const prerender = false;

export const POST: APIRoute = async ({ request, locals }) => {
  try {
    // @ts-ignore
    const env = locals.runtime.env;
    // @ts-ignore  
    const db = env.DB as D1Database;

    if (!db) {
      return new Response(JSON.stringify({ error: "D1 database not configured." }), { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Check if we already have QR codes
    const existingQRCodes = await db.prepare("SELECT COUNT(*) as count FROM qr_codes").first();
    
    if (existingQRCodes?.count === 0) {
      // Create sample QR codes first
      const sampleQRCodes = [
        {
          id: uuidv4(),
          name: 'Company Website',
          data: 'https://example.com',
          content_type: 'url',
          original_url: 'https://example.com',
          custom_slug: 'company-site',
          dynamic: 1
        },
        {
          id: uuidv4(),
          name: 'Contact Info',
          data: 'mailto:<EMAIL>',
          content_type: 'email',
          email_address: '<EMAIL>',
          custom_slug: 'contact-email',
          dynamic: 1
        },
        {
          id: uuidv4(),
          name: 'WiFi Access',
          data: 'WIFI:T:WPA;S:GuestNetwork;P:password123;;',
          content_type: 'wifi',
          wifi_ssid: 'GuestNetwork',
          custom_slug: 'guest-wifi',
          dynamic: 1
        }
      ];

      for (const qr of sampleQRCodes) {
        await db.prepare(`
          INSERT INTO qr_codes (id, name, data, content_type, original_url, email_address, wifi_ssid, custom_slug, dynamic, created_at)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now', '-' || (RANDOM() % 30) || ' days'))
        `).bind(
          qr.id,
          qr.name,
          qr.data,
          qr.content_type,
          qr.original_url || null,
          qr.email_address || null,
          qr.wifi_ssid || null,
          qr.custom_slug,
          qr.dynamic
        ).run();
      }

      // Create sample scan analytics data
      const countries = ['United States', 'Canada', 'United Kingdom', 'Germany', 'France', 'Japan', 'Australia', 'Brazil'];
      const cities = {
        'United States': ['New York', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix'],
        'Canada': ['Toronto', 'Vancouver', 'Montreal', 'Calgary', 'Ottawa'],
        'United Kingdom': ['London', 'Manchester', 'Birmingham', 'Leeds', 'Glasgow'],
        'Germany': ['Berlin', 'Munich', 'Hamburg', 'Cologne', 'Frankfurt'],
        'France': ['Paris', 'Lyon', 'Marseille', 'Toulouse', 'Nice'],
        'Japan': ['Tokyo', 'Osaka', 'Kyoto', 'Yokohama', 'Nagoya'],
        'Australia': ['Sydney', 'Melbourne', 'Brisbane', 'Perth', 'Adelaide'],
        'Brazil': ['São Paulo', 'Rio de Janeiro', 'Brasília', 'Salvador', 'Fortaleza']
      };
      const devices = ['mobile', 'desktop', 'tablet'];
      const browsers = ['Chrome', 'Safari', 'Firefox', 'Edge', 'Opera'];
      const operatingSystems = ['Windows', 'macOS', 'iOS', 'Android', 'Linux'];

      // Generate comprehensive scan data for the last 30 days
      for (let i = 0; i < 1000; i++) {
        const qrCode = sampleQRCodes[Math.floor(Math.random() * sampleQRCodes.length)];
        const country = countries[Math.floor(Math.random() * countries.length)];
        const city = cities[country][Math.floor(Math.random() * cities[country].length)];
        const device = devices[Math.floor(Math.random() * devices.length)];
        const browser = browsers[Math.floor(Math.random() * browsers.length)];
        const os = operatingSystems[Math.floor(Math.random() * operatingSystems.length)];

        // Generate random IP with some recurring IPs for unique user tracking
        let ip;
        if (Math.random() < 0.3) {
          // 30% chance of recurring IP (returning users)
          ip = `192.168.${Math.floor(Math.random() * 10)}.${Math.floor(Math.random() * 100)}`;
        } else {
          // 70% chance of new IP
          ip = `${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`;
        }

        // Generate weighted timestamp (more recent activity)
        let daysAgo, hoursAgo, minutesAgo;
        if (Math.random() < 0.4) {
          // 40% of scans in last 3 days
          daysAgo = Math.floor(Math.random() * 3);
          hoursAgo = Math.floor(Math.random() * 24);
          minutesAgo = Math.floor(Math.random() * 60);
        } else if (Math.random() < 0.7) {
          // 30% of scans in last week
          daysAgo = Math.floor(Math.random() * 7);
          hoursAgo = Math.floor(Math.random() * 24);
          minutesAgo = Math.floor(Math.random() * 60);
        } else {
          // 30% of scans in last 30 days
          daysAgo = Math.floor(Math.random() * 30);
          hoursAgo = Math.floor(Math.random() * 24);
          minutesAgo = Math.floor(Math.random() * 60);
        }

        // Generate realistic user agent
        const userAgents = {
          mobile: [
            'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1',
            'Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36',
            'Mozilla/5.0 (Linux; Android 10; Pixel 4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.91 Mobile Safari/537.36'
          ],
          desktop: [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0'
          ],
          tablet: [
            'Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
            'Mozilla/5.0 (Linux; Android 9; SM-T820) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Safari/537.36'
          ]
        };

        const userAgent = userAgents[device][Math.floor(Math.random() * userAgents[device].length)];

        await db.prepare(`
          INSERT INTO qr_code_scan_analytics (
            id, qr_code_id, scan_time, ip, user_agent, country, city, device, os, browser, created_at
          ) VALUES (?, ?, datetime('now', '-' || ? || ' days', '-' || ? || ' hours', '-' || ? || ' minutes'), ?, ?, ?, ?, ?, ?, ?, datetime('now', '-' || ? || ' days', '-' || ? || ' hours', '-' || ? || ' minutes'))
        `).bind(
          uuidv4(),
          qrCode.id,
          daysAgo,
          hoursAgo,
          minutesAgo,
          ip,
          userAgent,
          country,
          city,
          device,
          os,
          browser,
          daysAgo,
          hoursAgo,
          minutesAgo
        ).run();
      }
    }

    // Get current data counts
    const qrCount = await db.prepare("SELECT COUNT(*) as count FROM qr_codes").first();
    const scanCount = await db.prepare("SELECT COUNT(*) as count FROM qr_code_scan_analytics").first();

    return new Response(JSON.stringify({
      success: true,
      message: 'Test data created successfully',
      qrCodes: qrCount?.count || 0,
      scans: scanCount?.count || 0
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Test data creation error:', error);
    
    return new Response(JSON.stringify({ 
      error: error instanceof Error ? error.message : 'Failed to create test data' 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

export const GET: APIRoute = async ({ request, locals }) => {
  try {
    // @ts-ignore
    const env = locals.runtime.env;
    // @ts-ignore  
    const db = env.DB as D1Database;

    if (!db) {
      return new Response(JSON.stringify({ error: "D1 database not configured." }), { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get current data counts
    const qrCount = await db.prepare("SELECT COUNT(*) as count FROM qr_codes").first();
    const scanCount = await db.prepare("SELECT COUNT(*) as count FROM qr_code_scan_analytics").first();

    return new Response(JSON.stringify({
      qrCodes: qrCount?.count || 0,
      scans: scanCount?.count || 0
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Test data check error:', error);
    
    return new Response(JSON.stringify({ 
      error: error instanceof Error ? error.message : 'Failed to check test data' 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
