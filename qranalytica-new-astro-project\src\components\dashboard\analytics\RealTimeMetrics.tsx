import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '../../ui/card';
import { Badge } from '../../ui/badge';
import { 
  BarChart3, 
  Users, 
  QrCode, 
  TrendingUp, 
  TrendingDown, 
  Activity,
  Eye,
  MousePointer,
  Zap
} from 'lucide-react';

interface AnalyticsOverview {
  totalScans: number;
  uniqueUsers: number;
  totalQRCodes: number;
  scanVelocity: {
    hourly: number;
    daily: number;
  };
  conversionRate: number;
  topPerformers: Array<{
    id: string;
    name: string;
    scans: number;
    change: number;
  }>;
}

interface RealTimeMetricsProps {
  overview: AnalyticsOverview;
}

interface MetricCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  trend?: {
    value: number;
    label: string;
  };
  description?: string;
  gradient: string;
  iconBg: string;
  isRealTime?: boolean;
}

const AnimatedCounter: React.FC<{ value: number; duration?: number }> = ({ 
  value, 
  duration = 1000 
}) => {
  const [displayValue, setDisplayValue] = useState(0);

  useEffect(() => {
    let startTime: number;
    let animationFrame: number;

    const animate = (timestamp: number) => {
      if (!startTime) startTime = timestamp;
      const progress = Math.min((timestamp - startTime) / duration, 1);
      
      setDisplayValue(Math.floor(progress * value));
      
      if (progress < 1) {
        animationFrame = requestAnimationFrame(animate);
      }
    };

    animationFrame = requestAnimationFrame(animate);
    
    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }
    };
  }, [value, duration]);

  return <span>{displayValue.toLocaleString()}</span>;
};

const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  icon,
  trend,
  description,
  gradient,
  iconBg,
  isRealTime = false
}) => {
  const numericValue = typeof value === 'number' ? value : parseInt(value.toString().replace(/,/g, '')) || 0;
  const isPositiveTrend = trend && trend.value > 0;
  const isNegativeTrend = trend && trend.value < 0;

  return (
    <Card className="group relative overflow-hidden border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
      <div className={`absolute inset-0 bg-gradient-to-br ${gradient} opacity-5 group-hover:opacity-10 transition-opacity`} />
      
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3 relative z-10">
        <div className="flex items-center space-x-2">
          <CardTitle className="text-sm font-semibold text-gray-600 group-hover:text-gray-800 transition-colors">
            {title}
          </CardTitle>
          {isRealTime && (
            <Badge variant="secondary" className="bg-green-100 text-green-800 text-xs px-2 py-0.5">
              <div className="w-1.5 h-1.5 bg-green-500 rounded-full mr-1 animate-pulse" />
              Live
            </Badge>
          )}
        </div>
        <div className={`h-12 w-12 rounded-xl ${iconBg} flex items-center justify-center text-white shadow-lg group-hover:scale-110 transition-transform duration-300`}>
          <div className="h-6 w-6">
            {icon}
          </div>
        </div>
      </CardHeader>

      <CardContent className="relative z-10">
        <div className="text-3xl font-bold text-gray-900 mb-2 group-hover:text-gray-800 transition-colors">
          <AnimatedCounter value={numericValue} />
        </div>
        
        {trend && (
          <div className="flex items-center space-x-2 mb-2">
            <div className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${
              isPositiveTrend 
                ? 'bg-green-100 text-green-800' 
                : isNegativeTrend 
                  ? 'bg-red-100 text-red-800'
                  : 'bg-gray-100 text-gray-800'
            }`}>
              {isPositiveTrend ? (
                <TrendingUp className="h-3 w-3" />
              ) : isNegativeTrend ? (
                <TrendingDown className="h-3 w-3" />
              ) : null}
              <span>{Math.abs(trend.value)}%</span>
            </div>
            <span className="text-xs text-gray-500">{trend.label}</span>
          </div>
        )}
        
        {description && (
          <p className="text-sm text-gray-500 group-hover:text-gray-600 transition-colors">
            {description}
          </p>
        )}
      </CardContent>
    </Card>
  );
};

export const RealTimeMetrics: React.FC<RealTimeMetricsProps> = ({ overview }) => {
  const [lastUpdate, setLastUpdate] = useState(new Date());

  useEffect(() => {
    setLastUpdate(new Date());
  }, [overview]);

  const cards = [
    {
      title: 'Total Scans',
      value: overview.totalScans,
      icon: <BarChart3 className="h-6 w-6" />,
      description: 'All-time scan count',
      gradient: 'from-blue-500 to-blue-600',
      iconBg: 'bg-gradient-to-br from-blue-500 to-blue-600',
      isRealTime: true
    },
    {
      title: 'Unique Users',
      value: overview.uniqueUsers,
      icon: <Users className="h-6 w-6" />,
      description: 'Distinct visitors',
      gradient: 'from-emerald-500 to-emerald-600',
      iconBg: 'bg-gradient-to-br from-emerald-500 to-emerald-600',
      isRealTime: true
    },
    {
      title: 'Active QR Codes',
      value: overview.totalQRCodes,
      icon: <QrCode className="h-6 w-6" />,
      description: 'Currently active codes',
      gradient: 'from-purple-500 to-purple-600',
      iconBg: 'bg-gradient-to-br from-purple-500 to-purple-600'
    },
    {
      title: 'Scan Velocity',
      value: `${overview.scanVelocity.hourly}/hr`,
      icon: <Zap className="h-6 w-6" />,
      description: `${overview.scanVelocity.daily} scans today`,
      gradient: 'from-orange-500 to-orange-600',
      iconBg: 'bg-gradient-to-br from-orange-500 to-orange-600',
      isRealTime: true
    }
  ];

  return (
    <div className="space-y-6">
      {/* Real-time status indicator */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
          <span className="text-sm text-gray-600">
            Last updated: {lastUpdate.toLocaleTimeString()}
          </span>
        </div>
        <Badge variant="outline" className="text-green-700 border-green-200">
          <Activity className="h-3 w-3 mr-1" />
          Real-time data
        </Badge>
      </div>

      {/* Metrics cards */}
      <div className="grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
        {cards.map((card, index) => (
          <MetricCard
            key={index}
            title={card.title}
            value={card.value}
            icon={card.icon}
            description={card.description}
            gradient={card.gradient}
            iconBg={card.iconBg}
            isRealTime={card.isRealTime}
          />
        ))}
      </div>

      {/* Top performers section */}
      {overview.topPerformers && overview.topPerformers.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5" />
              <span>Top Performing QR Codes</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {overview.topPerformers.slice(0, 5).map((performer, index) => (
                <div
                  key={performer.id}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer transition-colors"
                  onClick={() => window.location.href = `/dashboard/qr-analytics/${performer.id}`}
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-semibold text-sm">
                      {index + 1}
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">{performer.name || `QR Code ${performer.id.slice(0, 8)}`}</p>
                      <p className="text-sm text-gray-500">{performer.scans.toLocaleString()} scans</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {performer.change !== 0 && (
                      <Badge
                        variant={performer.change > 0 ? "default" : "secondary"}
                        className={performer.change > 0 ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}
                      >
                        {performer.change > 0 ? '+' : ''}{performer.change}%
                      </Badge>
                    )}
                    <div className="text-gray-400">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default RealTimeMetrics;
