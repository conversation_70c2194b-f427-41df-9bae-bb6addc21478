import { type Config } from "tailwindcss";

export default {
  darkMode: ["class"],
  content: [
    "./src/**/*.{astro,html,js,jsx,ts,tsx}",
    "./components.json" // shadcn component registry
  ],
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        // Base semantic colors mapped to CSS variables defined in `global.css`
        background: "var(--background)",
        foreground: "var(--foreground)",

        primary: "var(--primary)",
        "primary-foreground": "var(--primary-foreground)",

        secondary: "var(--secondary)",
        "secondary-foreground": "var(--secondary-foreground)",

        accent: "var(--accent)",
        "accent-foreground": "var(--accent-foreground)",

        muted: "var(--muted)",
        "muted-foreground": "var(--muted-foreground)",

        destructive: "var(--destructive)",
        "destructive-foreground": "var(--destructive-foreground)",

        border: "var(--border)",
        input: "var(--input)",
        ring: "var(--ring)",

        // Additional highlight shades specified in `.cursorrules`
        "highlight-1": "#F39C12",
        "highlight-2": "#E74C3C",
        "highlight-3": "#8E44AD",
      },
      // You can freely extend other design-tokens (spacing, typography, etc.) here
    },
  },
  plugins: [],
} satisfies Config; 