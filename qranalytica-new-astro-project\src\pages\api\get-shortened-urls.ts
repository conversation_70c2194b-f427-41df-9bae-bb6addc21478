import type { APIRoute } from "astro";
export const prerender = false;

export const POST: APIRoute = async ({ request, locals }) => {
  // @ts-ignore
  const env = locals.runtime.env;
  // @ts-ignore
  const db = env.DB as D1Database;

  if (!db) {
    return new Response(JSON.stringify({ error: "D1 database not configured." }), { status: 500 });
  }

  const payload: any = await request.json();
  const { userEmail } = payload;

  if (!userEmail) {
    return new Response(JSON.stringify({ error: "User email is required" }), { status: 400 });
  }

  try {
    // Get user ID
    const user = await db.prepare(
      `SELECT id FROM users WHERE email = ?`
    ).bind(userEmail).first();

    if (!user) {
      return new Response(JSON.stringify({ urls: [] }), {
        headers: { "Content-Type": "application/json" },
        status: 200,
      });
    }

    // Get shortened URLs for this user with enhanced data
    const urls = await db.prepare(
      `SELECT 
        id, 
        name,
        original_url, 
        slug, 
        domain,
        clicks, 
        unique_clicks,
        analytics_enabled,
        created_at,
        updated_at
       FROM shortened_urls 
       WHERE user_id = ? 
       ORDER BY created_at DESC`
    ).bind(user.id).all();

    const formattedUrls = urls.results.map((row: any) => ({
      id: row.id,
      name: row.name || "Untitled Link",
      originalUrl: row.original_url,
      shortUrl: `https://${row.domain}/${row.slug}`,
      customSlug: row.slug,
      domain: row.domain || "qranalytica.com",
      clicks: row.clicks || 0,
      uniqueClicks: row.unique_clicks || 0,
      analyticsEnabled: row.analytics_enabled === 1,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
      analytics: {
        dailyClicks: [], // TODO: Implement daily click analytics
        topCountries: [], // TODO: Implement country analytics
        topDevices: [], // TODO: Implement device analytics
        topBrowsers: [], // TODO: Implement browser analytics
      }
    }));

    return new Response(JSON.stringify({ urls: formattedUrls }), {
      headers: { "Content-Type": "application/json" },
      status: 200,
    });
  } catch (err) {
    console.error("Database error:", err);
    return new Response(JSON.stringify({ error: "Failed to fetch shortened URLs" }), { status: 500 });
  }
}; 