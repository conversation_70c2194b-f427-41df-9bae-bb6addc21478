// Cloudflare API integration for Pages custom domains
// This module provides functions to interact with Cloudflare's API for domain management

interface CloudflareConfig {
  apiToken: string;
  accountId: string;
  zoneId?: string;
}

interface CloudflareDNSRecord {
  id?: string;
  type: string;
  name: string;
  content: string;
  ttl?: number;
  proxied?: boolean;
}

interface CloudflareCustomHostname {
  id?: string;
  hostname: string;
  ssl?: {
    status: string;
    method: string;
    type: string;
  };
  status: string;
  verification_errors?: string[];
}

export class CloudflareAPI {
  private config: CloudflareConfig;
  private baseUrl = 'https://api.cloudflare.com/client/v4';

  constructor(config: CloudflareConfig) {
    this.config = config;
  }

  private async makeRequest(endpoint: string, options: RequestInit = {}) {
    const url = `${this.baseUrl}${endpoint}`;
    const headers = {
      'Authorization': `Bearer ${this.config.apiToken}`,
      'Content-Type': 'application/json',
      ...options.headers,
    };

    const response = await fetch(url, {
      ...options,
      headers,
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({ error: 'Unknown error' }));
      throw new Error(`Cloudflare API error: ${response.status} - ${JSON.stringify(error)}`);
    }

    return response.json();
  }

  // List zones for the account
  async listZones() {
    return this.makeRequest(`/accounts/${this.config.accountId}/zones`);
  }

  // Get zone details by domain name
  async getZoneByDomain(domain: string) {
    const response = await this.makeRequest(`/zones?name=${domain}`);
    return response.result?.[0] || null;
  }

  // Create a DNS record
  async createDNSRecord(zoneId: string, record: CloudflareDNSRecord) {
    return this.makeRequest(`/zones/${zoneId}/dns_records`, {
      method: 'POST',
      body: JSON.stringify(record),
    });
  }

  // Update a DNS record
  async updateDNSRecord(zoneId: string, recordId: string, record: Partial<CloudflareDNSRecord>) {
    return this.makeRequest(`/zones/${zoneId}/dns_records/${recordId}`, {
      method: 'PUT',
      body: JSON.stringify(record),
    });
  }

  // Delete a DNS record
  async deleteDNSRecord(zoneId: string, recordId: string) {
    return this.makeRequest(`/zones/${zoneId}/dns_records/${recordId}`, {
      method: 'DELETE',
    });
  }

  // List DNS records for a zone
  async listDNSRecords(zoneId: string, type?: string, name?: string) {
    let endpoint = `/zones/${zoneId}/dns_records`;
    const params = new URLSearchParams();
    
    if (type) params.append('type', type);
    if (name) params.append('name', name);
    
    if (params.toString()) {
      endpoint += `?${params.toString()}`;
    }

    return this.makeRequest(endpoint);
  }

  // Pages API methods
  async listPagesProjects() {
    return this.makeRequest(`/accounts/${this.config.accountId}/pages/projects`);
  }

  async getPagesProject(projectName: string) {
    return this.makeRequest(`/accounts/${this.config.accountId}/pages/projects/${projectName}`);
  }

  // Custom hostname methods (for Cloudflare for SaaS)
  async createCustomHostname(zoneId: string, hostname: string) {
    const customHostname: CloudflareCustomHostname = {
      hostname,
      ssl: {
        method: 'http',
        type: 'dv',
        status: 'pending_validation'
      }
    };

    return this.makeRequest(`/zones/${zoneId}/custom_hostnames`, {
      method: 'POST',
      body: JSON.stringify(customHostname),
    });
  }

  async getCustomHostname(zoneId: string, customHostnameId: string) {
    return this.makeRequest(`/zones/${zoneId}/custom_hostnames/${customHostnameId}`);
  }

  async deleteCustomHostname(zoneId: string, customHostnameId: string) {
    return this.makeRequest(`/zones/${zoneId}/custom_hostnames/${customHostnameId}`, {
      method: 'DELETE',
    });
  }

  async listCustomHostnames(zoneId: string) {
    return this.makeRequest(`/zones/${zoneId}/custom_hostnames`);
  }

  // Domain verification methods
  async verifyDomainOwnership(domain: string, verificationToken: string): Promise<boolean> {
    try {
      // Check for TXT record with verification token
      const txtRecords = await this.lookupDNSRecord(domain, 'TXT', `_cf-custom-hostname.${domain}`);
      
      if (txtRecords && txtRecords.some((record: any) => 
        record.content && record.content.includes(verificationToken)
      )) {
        return true;
      }

      return false;
    } catch (error) {
      console.error('Domain verification error:', error);
      return false;
    }
  }

  // DNS lookup using Cloudflare's DNS over HTTPS
  async lookupDNSRecord(domain: string, type: string, name?: string): Promise<any[]> {
    try {
      const queryName = name || domain;
      const response = await fetch(
        `https://cloudflare-dns.com/dns-query?name=${queryName}&type=${type}`,
        {
          headers: {
            'Accept': 'application/dns-json',
          },
        }
      );

      if (!response.ok) {
        throw new Error(`DNS lookup failed: ${response.status}`);
      }

      const data = await response.json();
      return data.Answer || [];
    } catch (error) {
      console.error('DNS lookup error:', error);
      return [];
    }
  }

  // Helper method to setup domain for Pages
  async setupDomainForPages(domain: string, pagesProjectName: string) {
    try {
      // First, check if the domain's zone exists in Cloudflare
      const zone = await this.getZoneByDomain(domain);
      
      if (!zone) {
        throw new Error(`Domain ${domain} is not managed by Cloudflare. Please add it as a zone first.`);
      }

      // Create CNAME record pointing to Pages project
      const cnameRecord: CloudflareDNSRecord = {
        type: 'CNAME',
        name: domain,
        content: `${pagesProjectName}.pages.dev`,
        ttl: 1, // Auto TTL
        proxied: true, // Enable Cloudflare proxy
      };

      // Check if CNAME already exists
      const existingRecords = await this.listDNSRecords(zone.id, 'CNAME', domain);
      
      if (existingRecords.result && existingRecords.result.length > 0) {
        // Update existing record
        const recordId = existingRecords.result[0].id;
        await this.updateDNSRecord(zone.id, recordId, cnameRecord);
      } else {
        // Create new record
        await this.createDNSRecord(zone.id, cnameRecord);
      }

      return {
        success: true,
        zoneId: zone.id,
        message: 'Domain configured successfully for Pages'
      };
    } catch (error) {
      console.error('Error setting up domain for Pages:', error);
      throw error;
    }
  }

  // Remove domain from Pages
  async removeDomainFromPages(domain: string) {
    try {
      const zone = await this.getZoneByDomain(domain);
      
      if (!zone) {
        throw new Error(`Domain ${domain} is not managed by Cloudflare.`);
      }

      // Find and delete CNAME record
      const cnameRecords = await this.listDNSRecords(zone.id, 'CNAME', domain);
      
      if (cnameRecords.result && cnameRecords.result.length > 0) {
        for (const record of cnameRecords.result) {
          if (record.content && record.content.includes('.pages.dev')) {
            await this.deleteDNSRecord(zone.id, record.id);
          }
        }
      }

      return {
        success: true,
        message: 'Domain removed successfully from Pages'
      };
    } catch (error) {
      console.error('Error removing domain from Pages:', error);
      throw error;
    }
  }
}

// Factory function to create CloudflareAPI instance
export function createCloudflareAPI(env: any): CloudflareAPI {
  const config: CloudflareConfig = {
    apiToken: env.CLOUDFLARE_API_TOKEN,
    accountId: env.CLOUDFLARE_ACCOUNT_ID,
  };

  if (!config.apiToken || !config.accountId) {
    throw new Error('Cloudflare API credentials not configured. Please set CLOUDFLARE_API_TOKEN and CLOUDFLARE_ACCOUNT_ID environment variables.');
  }

  return new CloudflareAPI(config);
}

// Helper function to validate domain format
export function isValidDomain(domain: string): boolean {
  const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9])*$/;
  return domainRegex.test(domain) && domain.length <= 253;
}

// Helper function to extract root domain
export function getRootDomain(domain: string): string {
  const parts = domain.split('.');
  if (parts.length >= 2) {
    return parts.slice(-2).join('.');
  }
  return domain;
}
