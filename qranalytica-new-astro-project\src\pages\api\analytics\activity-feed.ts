import type { APIRoute } from 'astro';
import {
  getTimezoneFromRequest,
  getTimezoneAwareDateFilter,
  convertDataToUserTimezone,
  isValidTimezone
} from '../../../lib/timezone-utils';

export const prerender = false;

export const GET: APIRoute = async ({ request, locals }) => {
  try {
    // @ts-ignore
    const env = locals.runtime.env;
    // @ts-ignore  
    const db = env.DB as D1Database;

    if (!db) {
      return new Response(JSON.stringify({ error: "D1 database not configured." }), { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get URL parameters
    const url = new URL(request.url);
    const dateRange = url.searchParams.get('range') || '7d';
    const limit = parseInt(url.searchParams.get('limit') || '50');
    const qrCodeId = url.searchParams.get('qr_code_id');

    // Get user timezone
    const userTimezone = getTimezoneFromRequest(request);
    console.log('Debug: User timezone for activity feed:', userTimezone);

    // Validate timezone
    if (!isValidTimezone(userTimezone)) {
      console.warn('Invalid timezone provided:', userTimezone, 'falling back to UTC');
    }

    // Calculate timezone-aware date filter
    const dateFilter = getTimezoneAwareDateFilter(dateRange, userTimezone, 'scan.scan_time');

    // Add QR code filter if specified
    const qrCodeFilter = qrCodeId ? "AND scan.qr_code_id = ?" : "";

    // Get recent activity with QR code details
    let activitiesQuery = `
      SELECT
        scan.id,
        scan.qr_code_id as qrCodeId,
        qr.name as qrCodeName,
        scan.scan_time as scanTime,
        scan.ip,
        COALESCE(scan.country, 'Unknown') as country,
        COALESCE(scan.city, 'Unknown') as city,
        COALESCE(scan.device, 'Unknown') as device,
        COALESCE(scan.os, 'Unknown') as os,
        COALESCE(scan.browser, 'Unknown') as browser,
        scan.user_agent as userAgent
      FROM qr_code_scan_analytics scan
      LEFT JOIN qr_codes qr ON scan.qr_code_id = qr.id
      WHERE 1=1 ${dateFilter} ${qrCodeFilter}
      ORDER BY scan.scan_time DESC
      LIMIT ?
    `;
    const activitiesResult = qrCodeId ?
      await db.prepare(activitiesQuery).bind(qrCodeId, limit).all() :
      await db.prepare(activitiesQuery).bind(limit).all();

    // Convert scan times to user timezone
    const activitiesConverted = convertDataToUserTimezone(activitiesResult.results || [], userTimezone);

    return new Response(JSON.stringify({
      activities: activitiesConverted,
      timezone: userTimezone
    }), {
      status: 200,
      headers: { 
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    });

  } catch (error) {
    console.error('Activity feed API error:', error);
    
    return new Response(JSON.stringify({ 
      error: error instanceof Error ? error.message : 'Failed to fetch activity feed' 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
