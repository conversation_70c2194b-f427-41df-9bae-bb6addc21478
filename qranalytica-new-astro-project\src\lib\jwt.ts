import { SignJWT, jwtVerify } from "jose";
import type { JWTPayload } from "jose";

const encoder = new TextEncoder();

export async function signJwt(payload: JWTPayload, secret: string, expiresIn = "7d") {
  return new SignJWT(payload)
    .setProtectedHeader({ alg: "HS256" })
    .setExpirationTime(expiresIn)
    .sign(encoder.encode(secret));
}

export async function verifyJwt<T = JWTPayload>(token: string, secret: string): Promise<T | null> {
  try {
    const { payload } = await jwtVerify<T>(token, encoder.encode(secret));
    return payload;
  } catch {
    return null;
  }
} 