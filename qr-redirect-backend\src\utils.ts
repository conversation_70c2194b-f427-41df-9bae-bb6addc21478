import { DeviceInfo, GeoLocation } from './types';
import { UAParser } from 'ua-parser-js';

/**
 * Parse User-Agent string to extract device, OS, and browser information
 */
export function parseUserAgent(userAgent: string | null): DeviceInfo {
  if (!userAgent) {
    return { device: null, os: null, browser: null };
  }

  // Use UAParser for more accurate parsing
  const parser = new UAParser(userAgent);
  const userAgentData = parser.getResult();

  // Extract device type
  let device = 'Desktop';
  if (userAgentData.device.type === 'mobile') {
    device = 'Mobile';
  } else if (userAgentData.device.type === 'tablet') {
    device = 'Tablet';
  }

  // Extract OS information
  const os = userAgentData.os.name || null;

  // Extract browser information
  const browser = userAgentData.browser.name || null;

  return { device, os, browser };
}

/**
 * Get geolocation data from Cloudflare's request.cf properties
 */
export function getGeoLocation(request: Request): GeoLocation {
  // Get geolocation data with proper sanitization from Cloudflare's cf object
  const cf = request.cf as any; // Type assertion for Cloudflare properties
  const country = (cf?.country as string)?.trim() || null;
  const city = (cf?.city as string)?.trim() || null;
  const timezone = (
    (cf?.timezone as string)?.trim() ||
    Intl.DateTimeFormat().resolvedOptions().timeZone
  ).replace(/[^\w\/+-]/g, "");

  // For latitude and longitude, we would need to use an external service
  // or maintain a database of city coordinates. For now, we'll set them as null
  // and they can be populated later with a geolocation service if needed
  const lat = null;
  const lon = null;

  return { lat, lon, city, country, timezone };
}

/**
 * Get client IP address from request headers
 */
export function getClientIP(request: Request): string {
  // Get IP address with proper fallback handling
  const ip = (
    request.headers.get("CF-Connecting-IP") ||
    request.headers.get("X-Forwarded-For")?.split(",")[0] ||
    "unknown"
  ).trim();

  return ip;
}

/**
 * Get user agent string from request headers
 */
export function getUserAgent(request: Request): string {
  return request.headers.get("user-agent")?.trim() || "unknown";
}

/**
 * Get referer from request headers
 */
export function getReferer(request: Request): string | null {
  return request.headers.get("referer")?.trim() || null;
}

/**
 * Extract all analytics data from request
 */
export function extractAnalyticsData(request: Request) {
  const userAgent = getUserAgent(request);
  const referer = getReferer(request);
  const ip = getClientIP(request);

  // Parse user agent
  const deviceInfo = parseUserAgent(userAgent);

  // Get geolocation data
  const geoLocation = getGeoLocation(request);

  return {
    ip,
    userAgent,
    referer,
    ...deviceInfo,
    ...geoLocation
  };
}

/**
 * Generate a unique ID for analytics records
 */
export function generateAnalyticsId(): string {
  // Simple UUID v4 generation
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

/**
 * Validate if a URL is safe for redirection
 */
export function isValidRedirectUrl(url: string): boolean {
  try {
    const parsedUrl = new URL(url);
    // Only allow http and https protocols
    return parsedUrl.protocol === 'http:' || parsedUrl.protocol === 'https:';
  } catch {
    return false;
  }
}

/**
 * Create CORS headers for responses
 */
export function createCorsHeaders(origin: string | null, allowedOrigins: string): Headers {
  const headers = new Headers();
  
  // Handle CORS
  if (allowedOrigins === '*') {
    headers.set('Access-Control-Allow-Origin', '*');
  } else if (origin && allowedOrigins.split(',').includes(origin)) {
    headers.set('Access-Control-Allow-Origin', origin);
  }
  
  headers.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  headers.set('Access-Control-Max-Age', '86400');
  
  return headers;
}
