// Validation function to ensure QR options don't cause negative values
export const validateQROptions = (options: any) => {
  const validated = { ...options };
  
  // Ensure dimensions are reasonable
  validated.width = Math.max(100, Math.min(3000, validated.width || 300));
  validated.height = Math.max(100, Math.min(3000, validated.height || 300));
  
  // Ensure margin doesn't exceed half the width/height
  const maxMargin = Math.min(validated.width, validated.height) / 4;
  validated.margin = Math.max(0, Math.min(maxMargin, validated.margin || 20));
  
  // Validate image options to prevent negative positioning
  if (validated.imageOptions) {
    // Ensure imageSize is within reasonable bounds
    validated.imageOptions.imageSize = Math.max(0.1, Math.min(0.4, validated.imageOptions.imageSize || 0.3));
    
    // Calculate max margin based on QR size and image size
    const qrSize = Math.min(validated.width, validated.height) - (validated.margin * 2);
    const logoSize = qrSize * validated.imageOptions.imageSize;
    const maxImageMargin = Math.max(0, (qrSize - logoSize) / 4);
    
    validated.imageOptions.margin = Math.max(0, Math.min(maxImageMargin, validated.imageOptions.margin || 10));
  }
  
  return validated;
};

export const qrDefaultOptions: any = {
  width: 300,
  height: 300,
  data: "",
  image: "",
  margin: 20,
  dotsOptions: {
    color: "#000000",
    type: "square",
  },
  backgroundOptions: {
    color: "#ffffff",
  },
  imageOptions: {
    hideBackgroundDots: true,
    imageSize: 0.3,
    margin: 10,
    crossOrigin: "anonymous",
  },
  cornersSquareOptions: {
    type: "square",
    color: "#000000",
  },
  cornersDotOptions: {
    type: "dot",
    color: "#000000",
  },
  qrOptions: {
    errorCorrectionLevel: "H",
  },
}; 