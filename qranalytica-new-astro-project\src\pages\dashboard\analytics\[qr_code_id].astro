---
import DashboardLayout from '../../../layouts/DashboardLayout.astro';
import IndividualQRAnalytics from '../../../components/dashboard/IndividualQRAnalytics';

export const prerender = false;

// Get the QR code ID from the URL parameters
const { qr_code_id } = Astro.params;

if (!qr_code_id) {
  return Astro.redirect('/dashboard/analytics');
}
---

<DashboardLayout title={`QR Code Analytics - QRAnalytica`}>
  <IndividualQRAnalytics qrCodeId={qr_code_id} client:load />
</DashboardLayout>
