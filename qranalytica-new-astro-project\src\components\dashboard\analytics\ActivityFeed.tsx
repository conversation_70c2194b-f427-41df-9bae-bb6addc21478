import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../../ui/card';
import { Badge } from '../../ui/badge';
import { Button } from '../../ui/button';
import { 
  Activity, 
  MapPin, 
  Smartphone, 
  Clock,
  RefreshCw,
  Eye,
  Globe
} from 'lucide-react';
import { analyticsApi, formatDateInUserTimezone, formatTimeAgoInUserTimezone } from '../../../lib/api-utils';

interface ActivityItem {
  id: string;
  qrCodeId: string;
  qrCodeName: string;
  scanTime: string;
  ip: string;
  country: string;
  city: string;
  device: string;
  os: string;
  browser: string;
  userAgent: string;
}

interface ActivityFeedProps {
  dateRange: string;
  qrCodeId?: string;
}

export const ActivityFeed: React.FC<ActivityFeedProps> = ({ dateRange, qrCodeId }) => {
  const [activities, setActivities] = useState<ActivityItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(true);

  const fetchActivityFeed = async () => {
    try {
      setLoading(true);
      const result = await analyticsApi.fetchActivityFeed(dateRange, qrCodeId);
      setActivities(result.activities || []);
      setError(null);
    } catch (err) {
      console.error('Error fetching activity feed:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch activity feed');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchActivityFeed();
  }, [dateRange, qrCodeId]);

  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(fetchActivityFeed, 10000); // Refresh every 10 seconds
    return () => clearInterval(interval);
  }, [autoRefresh, dateRange, qrCodeId]);

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date();
    const scanTime = new Date(timestamp);
    const diffInSeconds = Math.floor((now.getTime() - scanTime.getTime()) / 1000);

    if (diffInSeconds < 60) {
      return `${diffInSeconds}s ago`;
    } else if (diffInSeconds < 3600) {
      return `${Math.floor(diffInSeconds / 60)}m ago`;
    } else if (diffInSeconds < 86400) {
      return `${Math.floor(diffInSeconds / 3600)}h ago`;
    } else {
      return `${Math.floor(diffInSeconds / 86400)}d ago`;
    }
  };

  const getDeviceIcon = (device: string) => {
    const deviceLower = device.toLowerCase();
    if (deviceLower.includes('mobile') || deviceLower.includes('phone')) {
      return <Smartphone className="h-4 w-4" />;
    }
    return <Smartphone className="h-4 w-4" />;
  };

  if (loading && activities.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Activity className="h-5 w-5" />
            <span>Real-Time Activity Feed</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="animate-pulse flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
                <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center">
            <Activity className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Failed to Load Activity Feed</h3>
            <p className="text-gray-600 mb-4">{error}</p>
            <Button onClick={fetchActivityFeed} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2">
              <Activity className="h-5 w-5" />
              <span>Real-Time Activity Feed</span>
              {autoRefresh && (
                <Badge variant="secondary" className="bg-green-100 text-green-800">
                  <div className="w-1.5 h-1.5 bg-green-500 rounded-full mr-1 animate-pulse" />
                  Live
                </Badge>
              )}
            </CardTitle>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setAutoRefresh(!autoRefresh)}
              >
                {autoRefresh ? 'Pause' : 'Resume'}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={fetchActivityFeed}
                disabled={loading}
              >
                <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {activities.length === 0 ? (
            <div className="text-center py-12">
              <Eye className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No Recent Activity</h3>
              <p className="text-gray-600">QR code scans will appear here in real-time</p>
            </div>
          ) : (
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {activities.map((activity) => (
                <div 
                  key={activity.id} 
                  className="flex items-start space-x-4 p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <div className="flex-shrink-0">
                    <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                      {getDeviceIcon(activity.device)}
                    </div>
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-1">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {activity.qrCodeName || `QR Code ${activity.qrCodeId.slice(0, 8)}`}
                      </p>
                      <div className="flex items-center space-x-1 text-xs text-gray-500">
                        <Clock className="h-3 w-3" />
                        <span>{formatTimeAgo(activity.scanTime)}</span>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-4 text-xs text-gray-600">
                      <div className="flex items-center space-x-1">
                        <MapPin className="h-3 w-3" />
                        <span>
                          {activity.city && activity.country 
                            ? `${activity.city}, ${activity.country}`
                            : activity.country || 'Unknown location'
                          }
                        </span>
                      </div>
                      
                      <div className="flex items-center space-x-1">
                        <Smartphone className="h-3 w-3" />
                        <span>{activity.device}</span>
                      </div>
                      
                      {activity.os && (
                        <div className="flex items-center space-x-1">
                          <Globe className="h-3 w-3" />
                          <span>{activity.os}</span>
                        </div>
                      )}
                    </div>
                    
                    {activity.browser && (
                      <div className="mt-1">
                        <Badge variant="outline" className="text-xs">
                          {activity.browser}
                        </Badge>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Activity Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Activity className="h-5 w-5" />
            <span>Activity Summary</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 grid-cols-1 sm:grid-cols-3">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <p className="text-2xl font-bold text-blue-600">{activities.length}</p>
              <p className="text-sm text-gray-600">Recent Scans</p>
            </div>
            
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <p className="text-2xl font-bold text-green-600">
                {new Set(activities.map(a => a.country)).size}
              </p>
              <p className="text-sm text-gray-600">Countries</p>
            </div>
            
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <p className="text-2xl font-bold text-purple-600">
                {new Set(activities.map(a => a.device)).size}
              </p>
              <p className="text-sm text-gray-600">Device Types</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ActivityFeed;
