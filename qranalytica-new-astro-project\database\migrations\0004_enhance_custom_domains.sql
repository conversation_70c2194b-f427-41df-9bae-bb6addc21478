-- Enhance custom domains table for Cloudflare Pages integration
-- Migration: 0004_enhance_custom_domains.sql

-- Add new columns to custom_domains table for comprehensive domain management
ALTER TABLE custom_domains ADD COLUMN status TEXT DEFAULT 'pending'; -- pending, active, failed, expired
ALTER TABLE custom_domains ADD COLUMN ssl_status TEXT DEFAULT 'pending'; -- pending, active, failed
ALTER TABLE custom_domains ADD COLUMN verification_method TEXT DEFAULT 'dns'; -- dns, http
ALTER TABLE custom_domains ADD COLUMN verification_token TEXT; -- Token for domain verification
ALTER TABLE custom_domains ADD COLUMN dns_records TEXT; -- JSON field for required DNS records
ALTER TABLE custom_domains ADD COLUMN cloudflare_zone_id TEXT; -- Cloudflare zone ID if managed by CF
ALTER TABLE custom_domains ADD COLUMN cloudflare_custom_hostname_id TEXT; -- CF custom hostname ID
ALTER TABLE custom_domains ADD COLUMN pages_project_name TEXT; -- Associated Pages project name
ALTER TABLE custom_domains ADD COLUMN last_verified_at DATETIME; -- Last successful verification
ALTER TABLE custom_domains ADD COLUMN expires_at DATETIME; -- SSL certificate expiration
ALTER TABLE custom_domains ADD COLUMN error_message TEXT; -- Error details if verification fails
ALTER TABLE custom_domains ADD COLUMN updated_at DATETIME DEFAULT CURRENT_TIMESTAMP;

-- Update existing records to have proper timestamps
UPDATE custom_domains SET updated_at = created_at WHERE updated_at IS NULL;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_custom_domains_user_id ON custom_domains (user_id);
CREATE INDEX IF NOT EXISTS idx_custom_domains_status ON custom_domains (status);
CREATE INDEX IF NOT EXISTS idx_custom_domains_domain ON custom_domains (domain);
CREATE INDEX IF NOT EXISTS idx_custom_domains_verified ON custom_domains (verified);
CREATE INDEX IF NOT EXISTS idx_custom_domains_pages_project ON custom_domains (pages_project_name);

-- Create triggers to handle updated_at timestamp
CREATE TRIGGER IF NOT EXISTS update_custom_domains_updated_at 
    AFTER UPDATE ON custom_domains
    FOR EACH ROW
    BEGIN
        UPDATE custom_domains SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

-- Create trigger to set updated_at on insert if not provided
CREATE TRIGGER IF NOT EXISTS insert_custom_domains_updated_at 
    AFTER INSERT ON custom_domains
    FOR EACH ROW
    WHEN NEW.updated_at IS NULL
    BEGIN
        UPDATE custom_domains SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;
