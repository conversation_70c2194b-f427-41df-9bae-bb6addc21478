import type { APIRoute } from 'astro';
import { createCloudflareAPI, isValidDomain } from '../../../lib/cloudflare-api';

export const prerender = false;

// POST - Setup domain with Cloudflare Pages
export const POST: APIRoute = async ({ request, locals }) => {
  try {
    // @ts-ignore
    const env = locals.runtime.env;
    // @ts-ignore  
    const db = env.DB as D1Database;

    if (!db) {
      return new Response(JSON.stringify({ error: "D1 database not configured." }), { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const body = await request.json();
    const { domain_id, action, user_id = 'default-user' } = body;

    if (!domain_id || !action) {
      return new Response(JSON.stringify({ 
        success: false,
        error: 'Domain ID and action are required' 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get domain details
    const domain = await db.prepare(`
      SELECT id, domain, pages_project_name, cloudflare_zone_id
      FROM custom_domains 
      WHERE id = ? AND user_id = ?
    `).bind(domain_id, user_id).first();

    if (!domain) {
      return new Response(JSON.stringify({ 
        success: false,
        error: 'Domain not found or access denied' 
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Check if Cloudflare API is configured
    if (!env.CLOUDFLARE_API_TOKEN || !env.CLOUDFLARE_ACCOUNT_ID) {
      return new Response(JSON.stringify({ 
        success: false,
        error: 'Cloudflare API not configured. Please contact administrator.' 
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const cfApi = createCloudflareAPI(env);

    try {
      if (action === 'setup') {
        // Setup domain for Pages
        const result = await cfApi.setupDomainForPages(
          domain.domain, 
          domain.pages_project_name || 'default-project'
        );

        // Update domain with Cloudflare zone ID
        await db.prepare(`
          UPDATE custom_domains 
          SET cloudflare_zone_id = ?, status = 'active', updated_at = CURRENT_TIMESTAMP
          WHERE id = ?
        `).bind(result.zoneId, domain_id).run();

        return new Response(JSON.stringify({
          success: true,
          message: 'Domain configured successfully with Cloudflare Pages',
          zone_id: result.zoneId
        }), {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        });

      } else if (action === 'remove') {
        // Remove domain from Pages
        await cfApi.removeDomainFromPages(domain.domain);

        // Update domain status
        await db.prepare(`
          UPDATE custom_domains 
          SET status = 'pending', cloudflare_zone_id = NULL, updated_at = CURRENT_TIMESTAMP
          WHERE id = ?
        `).bind(domain_id).run();

        return new Response(JSON.stringify({
          success: true,
          message: 'Domain removed successfully from Cloudflare Pages'
        }), {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        });

      } else {
        return new Response(JSON.stringify({ 
          success: false,
          error: 'Invalid action. Use "setup" or "remove".' 
        }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        });
      }

    } catch (cfError: any) {
      console.error('Cloudflare API error:', cfError);
      
      // Update domain with error
      await db.prepare(`
        UPDATE custom_domains 
        SET status = 'failed', error_message = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `).bind(cfError.message || 'Cloudflare API error', domain_id).run();

      return new Response(JSON.stringify({
        success: false,
        error: 'Cloudflare configuration failed',
        details: cfError.message
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

  } catch (error) {
    console.error('Error in Cloudflare integration:', error);
    return new Response(JSON.stringify({ 
      success: false,
      error: 'Failed to process Cloudflare request' 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

// GET - Check Cloudflare status for domain
export const GET: APIRoute = async ({ request, locals }) => {
  try {
    // @ts-ignore
    const env = locals.runtime.env;
    // @ts-ignore  
    const db = env.DB as D1Database;

    if (!db) {
      return new Response(JSON.stringify({ error: "D1 database not configured." }), { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const url = new URL(request.url);
    const domainId = url.searchParams.get('domain_id');
    const userId = url.searchParams.get('user_id') || 'default-user';

    if (!domainId) {
      return new Response(JSON.stringify({ 
        success: false,
        error: 'Domain ID is required' 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get domain details
    const domain = await db.prepare(`
      SELECT id, domain, cloudflare_zone_id, cloudflare_custom_hostname_id
      FROM custom_domains 
      WHERE id = ? AND user_id = ?
    `).bind(domainId, userId).first();

    if (!domain) {
      return new Response(JSON.stringify({ 
        success: false,
        error: 'Domain not found or access denied' 
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Check if Cloudflare API is configured
    if (!env.CLOUDFLARE_API_TOKEN || !env.CLOUDFLARE_ACCOUNT_ID) {
      return new Response(JSON.stringify({ 
        success: true,
        cloudflare_configured: false,
        message: 'Cloudflare API not configured' 
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    try {
      const cfApi = createCloudflareAPI(env);
      
      // Check if domain's zone exists in Cloudflare
      const zone = await cfApi.getZoneByDomain(domain.domain);
      
      let dnsRecords = null;
      if (zone && domain.cloudflare_zone_id) {
        // Get DNS records for the domain
        const records = await cfApi.listDNSRecords(zone.id, undefined, domain.domain);
        dnsRecords = records.result || [];
      }

      return new Response(JSON.stringify({
        success: true,
        cloudflare_configured: true,
        zone_exists: !!zone,
        zone_id: zone?.id,
        dns_records: dnsRecords,
        domain: {
          id: domain.id,
          domain: domain.domain,
          cloudflare_zone_id: domain.cloudflare_zone_id,
          cloudflare_custom_hostname_id: domain.cloudflare_custom_hostname_id
        }
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });

    } catch (cfError: any) {
      console.error('Cloudflare API error:', cfError);
      
      return new Response(JSON.stringify({
        success: true,
        cloudflare_configured: true,
        error: 'Failed to check Cloudflare status',
        details: cfError.message
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    }

  } catch (error) {
    console.error('Error checking Cloudflare status:', error);
    return new Response(JSON.stringify({ 
      success: false,
      error: 'Failed to check Cloudflare status' 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
