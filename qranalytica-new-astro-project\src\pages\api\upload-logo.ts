import type { APIRoute } from "astro";
import { v4 as uuidv4 } from "uuid";
export const prerender = false;

// Direct upload to R2 (following Cloudflare documentation pattern)
export const POST: APIRoute = async ({ request, locals }) => {
  try {
    console.log("Direct R2 upload API called");

    // @ts-ignore - Cloudflare env bindings are injected at runtime
    const env = (locals as any).runtime?.env;
    console.log("Environment available:", !!env);

    // Check if we're in development mode (no env available)
    const isDevelopment = !env;

    if (isDevelopment) {
      console.log("Development mode detected - simulating upload");

      const formData = await request.formData();
      const file = formData.get("file");

      if (!file || !(file instanceof File)) {
        return new Response(JSON.stringify({ error: "No file provided" }), { status: 400 });
      }

      // In development, return a mock URL but keep the base64 for preview
      const ext = file.type.split("/").pop() || "png";
      const mockKey = `dev-${Date.now()}-${uuidv4()}.${ext}`;
      const mockUrl = `https://cdn.qranalytica.com/${mockKey}`;

      console.log("Development mode - returning mock URL:", mockUrl);

      return new Response(JSON.stringify({
        url: mockUrl,
        key: mockKey,
        development: true,
        message: "Development mode - file not actually uploaded"
      }), {
        headers: { "Content-Type": "application/json" },
        status: 201
      });
    }

    // Production mode with R2
    // @ts-ignore
    const bucket = env.QR_LOGOS as R2Bucket;
    console.log("Bucket available:", !!bucket);

    if (!bucket) {
      console.error("R2 bucket not configured");
      return new Response(JSON.stringify({ error: "R2 bucket not configured." }), { status: 500 });
    }

    const formData = await request.formData();
    const file = formData.get("file");

    if (!file || !(file instanceof File)) {
      console.error("No valid file provided");
      return new Response(JSON.stringify({ error: "No file provided" }), { status: 400 });
    }

    console.log("File details:", {
      name: file.name,
      type: file.type,
      size: file.size
    });

    const ext = file.type.split("/").pop() || "png";
    const key = `${Date.now()}-${uuidv4()}.${ext}`;
    console.log("Generated key:", key);

    // Convert file to ArrayBuffer for R2 upload
    const fileBuffer = await file.arrayBuffer();
    console.log("File buffer size:", fileBuffer.byteLength);

    // Upload directly to R2 using the Workers API
    console.log("Uploading to R2...");
    const result = await bucket.put(key, fileBuffer, {
      httpMetadata: {
        contentType: file.type,
        cacheControl: "public, max-age=31536000" // Cache for 1 year
      }
    });

    console.log("Upload result:", !!result);

    // In development with platform proxy, always use Wrangler CLI for real uploads
    // Platform proxy simulates R2 operations but doesn't actually upload files
    let actuallyUploaded = false;
    const isProduction = true

    console.log("Environment check - NODE_ENV:", process.env.NODE_ENV);
    console.log("Is production:", isProduction);

    // if (!isProduction) {
    //   console.log("Development mode detected - using Wrangler CLI for real upload");

    //   // Use Wrangler CLI to actually upload the file in development
    //   try {
    //     const fs = await import('fs');
    //     const path = await import('path');
    //     const { exec } = await import('child_process');
    //     const { promisify } = await import('util');
    //     const execAsync = promisify(exec);

    //     // Create temp directory if it doesn't exist
    //     const tempDir = path.join(process.cwd(), 'temp');
    //     if (!fs.existsSync(tempDir)) {
    //       fs.mkdirSync(tempDir, { recursive: true });
    //     }

    //     // Save file temporarily
    //     const tempFilePath = path.join(tempDir, `temp-${Date.now()}.${ext}`);
    //     fs.writeFileSync(tempFilePath, Buffer.from(fileBuffer));

    //     // Upload using Wrangler CLI
    //     const command = `npx wrangler r2 object put qranalytica/${key} --file="${tempFilePath}" --content-type="${file.type}"`;
    //     console.log("Executing Wrangler command:", command);

    //     const { stdout, stderr } = await execAsync(command);
    //     console.log("Wrangler upload stdout:", stdout);
    //     if (stderr) console.log("Wrangler upload stderr:", stderr);

    //     // Clean up temp file
    //     fs.unlinkSync(tempFilePath);

    //     console.log("Real upload via Wrangler CLI successful");
    //     actuallyUploaded = true;
    //   } catch (fallbackError) {
    //     console.error("Wrangler CLI upload failed:", fallbackError);
    //     return new Response(JSON.stringify({
    //       error: "Upload failed",
    //       details: "Wrangler CLI upload failed: " + (fallbackError instanceof Error ? fallbackError.message : String(fallbackError))
    //     }), { status: 500 });
    //   }
    // } else {
      // Production environment - use the R2 API directly and verify
      console.log("Production mode - using R2 API directly");
      try {
        const headResult = await bucket.head(key);
        console.log("File verification successful:", !!headResult);
        actuallyUploaded = true;
      } catch (verifyError) {
        console.error("File verification failed:", verifyError);
        actuallyUploaded = false;
      }
    // }

    // Public URL for accessing the uploaded file
    const publicHost = `cdn.qranalytica.com`;
    const finalUrl = `https://${publicHost}/${key}`;
    console.log("Final URL:", finalUrl);

    return new Response(JSON.stringify({
      url: finalUrl,
      key,
      uploaded: true,
      verified: actuallyUploaded,
      method: actuallyUploaded ? "wrangler-cli" : "platform-proxy"
    }), { headers: { "Content-Type": "application/json" }, status: 201 });
  } catch (error) {
    console.error("Upload error:", error);
    return new Response(JSON.stringify({
      error: "Upload failed",
      details: error instanceof Error ? error.message : String(error)
    }), { status: 500 });
  }
};

