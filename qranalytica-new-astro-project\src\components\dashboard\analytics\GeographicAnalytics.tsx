import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../../ui/card';
import { Badge } from '../../ui/badge';
import { Button } from '../../ui/button';
import { 
  Globe, 
  MapPin, 
  TrendingUp, 
  Users,
  BarChart3,
  RefreshCw
} from 'lucide-react';
import {
  PieChart,
  Pie,
  Cell,
  ResponsiveContainer,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend
} from 'recharts';
import { analyticsApi } from '../../../lib/api-utils';

interface GeographicData {
  countries: Array<{
    country: string;
    scans: number;
    percentage: number;
    cities: Array<{
      city: string;
      scans: number;
      percentage: number;
    }>;
  }>;
  topCities: Array<{
    city: string;
    country: string;
    scans: number;
    percentage: number;
  }>;
  totalScans: number;
}

interface GeographicAnalyticsProps {
  dateRange: string;
  qrCodeId?: string;
}

const COLORS = [
  '#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6',
  '#06B6D4', '#84CC16', '#F97316', '#EC4899', '#6366F1'
];

export const GeographicAnalytics: React.FC<GeographicAnalyticsProps> = ({ dateRange, qrCodeId }) => {
  const [data, setData] = useState<GeographicData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedCountry, setSelectedCountry] = useState<string | null>(null);

  const fetchGeographicData = async () => {
    try {
      setLoading(true);
      const result = await analyticsApi.fetchGeographic(dateRange, qrCodeId);
      setData(result);
      setError(null);
    } catch (err) {
      console.error('Error fetching geographic data:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch geographic data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchGeographicData();
  }, [dateRange, qrCodeId]);

  const handleCountryClick = (country: string) => {
    setSelectedCountry(selectedCountry === country ? null : country);
  };

  const getSelectedCountryData = () => {
    if (!selectedCountry || !data) return null;
    return data.countries.find(c => c.country === selectedCountry);
  };

  if (loading) {
    return (
      <div className="grid gap-6 grid-cols-1 lg:grid-cols-2">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader>
              <div className="h-5 bg-gray-200 rounded w-1/2"></div>
            </CardHeader>
            <CardContent>
              <div className="h-64 bg-gray-200 rounded"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center">
            <Globe className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Failed to Load Geographic Data</h3>
            <p className="text-gray-600 mb-4">{error}</p>
            <Button onClick={fetchGeographicData} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!data) return null;

  const selectedCountryData = getSelectedCountryData();

  return (
    <div className="space-y-6">
      {/* Header with stats */}
      <div className="grid gap-4 grid-cols-1 sm:grid-cols-3">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Globe className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm font-medium text-gray-600">Countries</p>
                <p className="text-2xl font-bold text-gray-900">{data.countries.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <MapPin className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm font-medium text-gray-600">Cities</p>
                <p className="text-2xl font-bold text-gray-900">{data.topCities.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5 text-purple-600" />
              <div>
                <p className="text-sm font-medium text-gray-600">Total Scans</p>
                <p className="text-2xl font-bold text-gray-900">{data.totalScans.toLocaleString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 grid-cols-1 lg:grid-cols-2">
        {/* Country Distribution Pie Chart */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Globe className="h-5 w-5" />
              <span>Global Distribution</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={data.countries.slice(0, 10)}
                  cx="50%"
                  cy="50%"
                  outerRadius={100}
                  innerRadius={40}
                  paddingAngle={2}
                  dataKey="scans"
                  nameKey="country"
                >
                  {data.countries.slice(0, 10).map((entry, index) => (
                    <Cell
                      key={`cell-${index}`}
                      fill={COLORS[index % COLORS.length]}
                      className="cursor-pointer hover:opacity-80"
                      onClick={() => handleCountryClick(entry.country)}
                    />
                  ))}
                </Pie>
                <Tooltip
                  formatter={(value: any, name: any) => [
                    `${value.toLocaleString()} scans`,
                    name
                  ]}
                />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Top Countries Bar Chart */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5" />
              <span>Top Countries</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={data.countries.slice(0, 8)} layout="horizontal">
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis type="number" />
                <YAxis dataKey="country" type="category" width={80} />
                <Tooltip
                  formatter={(value: any) => [`${value.toLocaleString()} scans`, 'Scans']}
                />
                <Bar
                  dataKey="scans"
                  fill="#3B82F6"
                  radius={[0, 4, 4, 0]}
                />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Top Cities Section */}
      {data.topCities && data.topCities.length > 0 ? (
        <div className="grid gap-6 grid-cols-1 lg:grid-cols-2">
          {/* Top Cities Bar Chart */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <MapPin className="h-5 w-5" />
                <span>Top Cities Worldwide</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={data.topCities.slice(0, 10)} layout="horizontal">
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis type="number" />
                  <YAxis
                    dataKey="city"
                    type="category"
                    width={100}
                    tick={{ fontSize: 12 }}
                  />
                  <Tooltip
                    formatter={(value: any, name: any, props: any) => [
                      `${value.toLocaleString()} scans`,
                      `${props.payload.city}, ${props.payload.country}`
                    ]}
                  />
                  <Bar
                    dataKey="scans"
                    fill="#10B981"
                    radius={[0, 4, 4, 0]}
                  />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Cities Grid */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Users className="h-5 w-5" />
                <span>City Performance</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 max-h-80 overflow-y-auto">
                {data.topCities.slice(0, 15).map((city, index) => (
                  <div key={`${city.city}-${city.country}`} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                    <div className="flex items-center space-x-3">
                      <div className="flex items-center justify-center w-8 h-8 bg-green-100 text-green-700 rounded-full text-sm font-semibold">
                        {index + 1}
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">{city.city}</p>
                        <p className="text-sm text-gray-600">{city.country}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-gray-900">{city.scans.toLocaleString()}</p>
                      <Badge variant="outline" className="text-xs">
                        {city.percentage.toFixed(1)}%
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      ) : (
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="text-center">
              <MapPin className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No City Data Available</h3>
              <p className="text-gray-600">
                City information will appear here once QR codes are scanned from different locations.
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Comprehensive Cities Table */}
      {data.topCities && data.topCities.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5" />
              <span>All Cities Breakdown</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-3 px-4 font-semibold text-gray-700">Rank</th>
                    <th className="text-left py-3 px-4 font-semibold text-gray-700">City</th>
                    <th className="text-left py-3 px-4 font-semibold text-gray-700">Country</th>
                    <th className="text-right py-3 px-4 font-semibold text-gray-700">Scans</th>
                    <th className="text-right py-3 px-4 font-semibold text-gray-700">Percentage</th>
                  </tr>
                </thead>
                <tbody>
                  {data.topCities.map((city, index) => (
                    <tr key={`${city.city}-${city.country}`} className="border-b hover:bg-gray-50">
                      <td className="py-3 px-4">
                        <div className="flex items-center justify-center w-6 h-6 bg-blue-100 text-blue-700 rounded-full text-sm font-semibold">
                          {index + 1}
                        </div>
                      </td>
                      <td className="py-3 px-4 font-medium text-gray-900">{city.city}</td>
                      <td className="py-3 px-4 text-gray-600">{city.country}</td>
                      <td className="text-right py-3 px-4 font-mono font-semibold">
                        {city.scans.toLocaleString()}
                      </td>
                      <td className="text-right py-3 px-4">
                        <Badge variant="outline">
                          {city.percentage.toFixed(1)}%
                        </Badge>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Country Details Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <MapPin className="h-5 w-5" />
              <span>Country Breakdown</span>
            </div>
            {selectedCountry && (
              <Badge variant="secondary">
                Showing details for {selectedCountry}
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3 px-4 font-semibold text-gray-700">Country</th>
                  <th className="text-right py-3 px-4 font-semibold text-gray-700">Scans</th>
                  <th className="text-right py-3 px-4 font-semibold text-gray-700">Percentage</th>
                  <th className="text-right py-3 px-4 font-semibold text-gray-700">Cities</th>
                </tr>
              </thead>
              <tbody>
                {data.countries.map((country, index) => (
                  <tr 
                    key={country.country}
                    className={`border-b hover:bg-gray-50 cursor-pointer ${
                      selectedCountry === country.country ? 'bg-blue-50' : ''
                    }`}
                    onClick={() => handleCountryClick(country.country)}
                  >
                    <td className="py-3 px-4">
                      <div className="flex items-center space-x-2">
                        <div 
                          className="w-3 h-3 rounded-full"
                          style={{ backgroundColor: COLORS[index % COLORS.length] }}
                        />
                        <span className="font-medium">{country.country}</span>
                      </div>
                    </td>
                    <td className="text-right py-3 px-4 font-mono">
                      {country.scans.toLocaleString()}
                    </td>
                    <td className="text-right py-3 px-4">
                      {country.percentage.toFixed(1)}%
                    </td>
                    <td className="text-right py-3 px-4">
                      {country.cities.length}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Selected Country Cities */}
      {selectedCountryData && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <MapPin className="h-5 w-5" />
              <span>Cities in {selectedCountry}</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-3 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
              {selectedCountryData.cities.map((city, index) => (
                <div key={city.city} className="p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-gray-900">{city.city}</p>
                      <p className="text-sm text-gray-600">{city.scans.toLocaleString()} scans</p>
                    </div>
                    <Badge variant="outline">
                      {city.percentage.toFixed(1)}%
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default GeographicAnalytics;
