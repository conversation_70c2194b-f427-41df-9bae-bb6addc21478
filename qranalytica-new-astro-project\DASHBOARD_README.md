# QR Analytics Dashboard

A professional admin dashboard for tracking QR codes, built as a comprehensive SaaS tool with modern UI/UX and powerful analytics capabilities.

## 🚀 Features

### Dashboard Overview
- **Real-time Analytics**: Live statistics showing total scans, active QR codes, unique users, and conversion rates
- **Visual Charts**: Interactive charts displaying scan trends, device breakdown, and geographical distribution
- **Performance Metrics**: Key performance indicators with percentage changes and trend analysis
- **Top Performers**: Ranking of best performing QR codes with growth metrics

### QR Code Management
- **Complete CRUD Operations**: Create, read, update, and delete QR codes
- **Advanced Search & Filtering**: Search by name, URL, status, and other criteria
- **Bulk Operations**: Manage multiple QR codes simultaneously
- **Status Management**: Track active, paused, and expired QR codes
- **Quick Actions**: One-click access to analytics, editing, and sharing

### Analytics & Insights
- **Geographical Analytics**: Track scans by country and city with percentage breakdowns
- **Device Analytics**: Monitor desktop, mobile, and tablet usage patterns
- **Behavioral Analytics**: Understand user interaction patterns and timing
- **Real-time Monitoring**: Live scan tracking with detailed user information
- **Historical Data**: Access to historical scan data and trends

### Professional UI/UX
- **Modern Design**: Clean, professional interface following SaaS design principles
- **Responsive Layout**: Fully responsive design that works on all devices
- **Dark/Light Mode**: Theme switching for user preference
- **Accessibility**: WCAG compliant with keyboard navigation and screen reader support
- **Performance Optimized**: Fast loading times with optimized components

## 📊 Dashboard Sections

### 1. Overview Tab
- **Key Metrics Cards**: Total scans, QR codes, users, conversion rate
- **Trend Charts**: 7-day scan activity with visual representations
- **Geographic Distribution**: Top countries with scan percentages
- **Top Performing QR Codes**: Ranked list with performance changes

### 2. QR Codes Tab
- **Management Interface**: Complete QR code management system
- **Search & Filter**: Advanced filtering by status, type, date range
- **Table View**: Comprehensive table with all QR code details
- **Quick Actions**: View analytics, edit, download, delete
- **Batch Operations**: Select multiple QR codes for bulk actions

### 3. Analytics Tab
- **Device Breakdown**: Mobile, desktop, tablet usage statistics
- **Time-based Analysis**: Hourly, daily, weekly, monthly trends
- **Geographic Insights**: Country and city-level analytics
- **User Behavior**: Scan patterns and user journey analysis
- **Export Capabilities**: Download reports in various formats

### 4. Settings Tab
- **Account Management**: User profile and preferences
- **Notification Settings**: Configure email and push notifications
- **API Configuration**: Manage API keys and webhooks
- **Team Management**: Add/remove team members and permissions

## 🛠 Technical Implementation

### Frontend Components
```
src/components/
├── Dashboard.tsx          # Main dashboard component
├── DashboardStats.tsx     # Statistics and charts component
├── QRCodeManager.tsx      # QR code management interface
├── AnalyticsView.tsx      # Detailed analytics view
└── ui/                    # Reusable UI components
```

### API Endpoints
```
src/pages/api/
├── dashboard-analytics.ts # Dashboard overview data
├── qr-codes.ts           # QR code CRUD operations
├── qr-analytics/[id].ts  # Individual QR analytics
└── export-data.ts        # Data export functionality
```

### Database Schema
```sql
-- QR Codes table
CREATE TABLE qr_codes (
  id TEXT PRIMARY KEY,
  name TEXT,
  original_url TEXT,
  custom_slug TEXT,
  content_type TEXT,
  content_data TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  expires_at DATETIME,
  user_id TEXT
);

-- Analytics table
CREATE TABLE qr_code_scan_analytics (
  id TEXT PRIMARY KEY,
  qr_code_id TEXT,
  ip TEXT,
  user_agent TEXT,
  referrer TEXT,
  lat REAL,
  lon REAL,
  city TEXT,
  country TEXT,
  device TEXT,
  os TEXT,
  browser TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (qr_code_id) REFERENCES qr_codes(id)
);
```

## 🎨 Design System

### Color Palette
- **Primary**: Blue (#3B82F6) - Main brand color
- **Secondary**: Indigo (#6366F1) - Accent color
- **Success**: Green (#10B981) - Positive actions
- **Warning**: Yellow (#F59E0B) - Caution states
- **Error**: Red (#EF4444) - Error states
- **Neutral**: Gray (#6B7280) - Text and borders

### Typography
- **Headings**: Inter font family, various weights
- **Body Text**: Inter Regular (400)
- **Emphasis**: Inter Medium (500) and Semibold (600)
- **Code**: JetBrains Mono for technical content

### Spacing System
- **Base Unit**: 4px (0.25rem)
- **Common Spacing**: 4px, 8px, 12px, 16px, 24px, 32px, 48px, 64px
- **Container Max Width**: 1280px (7xl)
- **Component Padding**: 16px-24px standard

## 📱 Responsive Design

### Breakpoints
- **Mobile**: 0-640px (sm)
- **Tablet**: 641-768px (md)
- **Desktop**: 769-1024px (lg)
- **Large Desktop**: 1025px+ (xl)

### Mobile Optimizations
- **Touch-friendly**: Minimum 44px touch targets
- **Swipe Gestures**: Support for mobile navigation
- **Optimized Tables**: Horizontal scroll and card layouts
- **Compressed Navigation**: Collapsible menu system

## 🔐 Security Features

### Authentication
- **Google OAuth**: Secure authentication via Google
- **Session Management**: JWT-based session handling
- **Role-based Access**: Different permission levels
- **API Security**: Rate limiting and authentication

### Data Protection
- **Input Validation**: All inputs validated and sanitized
- **SQL Injection Prevention**: Parameterized queries
- **XSS Protection**: Content Security Policy headers
- **HTTPS Only**: Secure transmission of all data

## 📈 Performance Metrics

### Loading Performance
- **First Contentful Paint**: < 1.5s
- **Largest Contentful Paint**: < 2.5s
- **Cumulative Layout Shift**: < 0.1
- **First Input Delay**: < 100ms

### Optimization Techniques
- **Code Splitting**: Lazy loading of components
- **Image Optimization**: WebP format with fallbacks
- **Caching Strategy**: Browser and CDN caching
- **Bundle Optimization**: Tree shaking and minification

## 🚀 Deployment

### Development
```bash
npm run dev
```

### Production Build
```bash
npm run build
npm run preview
```

### Cloudflare Pages Deployment
```bash
npm run deploy
```

## 🔧 Configuration

### Environment Variables
```env
# Database
DATABASE_URL=your_d1_database_url

# Authentication
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# Analytics
ANALYTICS_API_KEY=your_analytics_key
```

### Feature Flags
- **ENABLE_REAL_TIME**: Enable real-time updates
- **ENABLE_EXPORTS**: Enable data export features
- **ENABLE_TEAM_FEATURES**: Enable team collaboration
- **ENABLE_ADVANCED_ANALYTICS**: Enable advanced analytics

## 📊 Analytics Integration

### Tracking Events
- **QR Code Creation**: Track when QR codes are created
- **Scan Events**: Monitor all QR code scans
- **User Actions**: Track dashboard interactions
- **Performance Metrics**: Monitor system performance

### Data Export
- **CSV Export**: Export data in CSV format
- **PDF Reports**: Generate PDF analytics reports
- **API Access**: Programmatic access to data
- **Real-time Webhooks**: Push data to external systems

## 🤝 Contributing

### Development Guidelines
1. Follow TypeScript best practices
2. Use existing UI components when possible
3. Write comprehensive tests for new features
4. Follow the established design system
5. Ensure mobile responsiveness

### Code Style
- **ESLint**: Follow the established linting rules
- **Prettier**: Use automatic code formatting
- **TypeScript**: Maintain strict type checking
- **Comments**: Document complex logic and APIs

## 📞 Support

For technical support or feature requests:
- **Email**: <EMAIL>
- **Documentation**: [docs.qranalytica.com](https://docs.qranalytica.com)
- **GitHub Issues**: [github.com/qranalytica/issues](https://github.com/qranalytica/issues)
- **Community**: [community.qranalytica.com](https://community.qranalytica.com)

---

**Built with ❤️ using Astro, React, TypeScript, and Tailwind CSS** 