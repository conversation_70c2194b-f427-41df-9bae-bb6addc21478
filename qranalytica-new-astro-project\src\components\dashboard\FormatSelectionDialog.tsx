import React, { useState } from "react";
import { Download, X } from "lucide-react";
import { Button } from "../ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "../ui/dialog";
import { Label } from "../ui/label";

interface FormatSelectionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onDownload: (format: "png" | "jpeg" | "svg") => void;
  qrCodeName?: string;
}

export const FormatSelectionDialog: React.FC<FormatSelectionDialogProps> = ({
  isOpen,
  onClose,
  onDownload,
  qrCodeName = "QR Code"
}) => {
  const [selectedFormat, setSelectedFormat] = useState<"png" | "jpeg" | "svg">("png");

  const handleDownload = () => {
    onDownload(selectedFormat);
    onClose();
  };

  const formatOptions = [
    {
      value: "png" as const,
      label: "PNG",
      description: "High quality, lossless",
      recommended: true
    },
    {
      value: "svg" as const,
      label: "SVG", 
      description: "Vector, infinitely scalable",
      recommended: false
    },
    {
      value: "jpeg" as const,
      label: "JPEG",
      description: "Smaller file size, compressed",
      recommended: false
    }
  ];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Download className="w-5 h-5 text-primary" />
            Download QR Code
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          <div>
            <Label className="text-base font-medium">Choose Download Format</Label>
            <p className="text-sm text-muted-foreground mt-1">
              Select the file format for "{qrCodeName}"
            </p>
          </div>
          
          <div className="space-y-3">
            {formatOptions.map((option) => (
              <div
                key={option.value}
                className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                  selectedFormat === option.value
                    ? "border-primary bg-primary/5"
                    : "border-gray-200 hover:border-gray-300"
                }`}
                onClick={() => setSelectedFormat(option.value)}
              >
                <div className="flex items-center gap-3">
                  <div className={`w-4 h-4 rounded-full border-2 ${
                    selectedFormat === option.value
                      ? "border-primary bg-primary"
                      : "border-gray-300"
                  }`}>
                    {selectedFormat === option.value && (
                      <div className="w-2 h-2 bg-white rounded-full m-0.5" />
                    )}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-sm">{option.label}</span>
                      {option.recommended && (
                        <span className="text-xs bg-blue-100 text-blue-700 px-2 py-0.5 rounded-full">
                          Recommended
                        </span>
                      )}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {option.description}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="flex justify-end gap-3 mt-6">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleDownload} className="gap-2">
            <Download className="w-4 h-4" />
            Download {selectedFormat.toUpperCase()}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
