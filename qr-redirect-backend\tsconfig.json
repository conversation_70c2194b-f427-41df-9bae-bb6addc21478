{"compilerOptions": {"target": "ES2022", "lib": ["ES2022", "WebWorker"], "module": "ESNext", "moduleResolution": "bundler", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "checkJs": false, "declaration": true, "declarationMap": true, "skipLibCheck": true, "strict": true, "noEmit": true, "preserveWatchOutput": true, "resolveJsonModule": true, "isolatedModules": true, "forceConsistentCasingInFileNames": true, "types": ["@cloudflare/workers-types/2023-07-01"]}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}