export interface QRCode {
  id: string;
  user_id: string | null;
  name: string | null;
  data: string;
  dynamic: number;
  tracking_domain: string | null;
  custom_slug: string | null;
  redirect_url: string | null;
  content_type: string;
  content_data: string | null;
  original_url: string | null;
  email_address: string | null;
  wifi_ssid: string | null;
  created_at: string;
  updated_at: string | null;
  scanCount: number;
  status: 'active' | 'inactive';
  lastScanned?: string;
}

export interface DashboardMetrics {
  totalActiveQRCodes: number;
  totalScanCount: number;
  todayScanCount: number;
  weeklyGrowth?: number;
  monthlyGrowth?: number;
}

export interface QRAnalytics {
  qrCodeId: string;
  totalScans: number;
  uniqueScans: number;
  scansByDate: ScanByDate[];
  scansByLocation: ScanByLocation[];
  scansByDevice: ScanByDevice[];
  scansByReferrer: ScanByReferrer[];
  recentScans: RecentScan[];
}

export interface ScanByDate {
  date: string;
  scans: number;
}

export interface ScanByLocation {
  country: string;
  city?: string;
  scans: number;
  percentage: number;
}

export interface ScanByDevice {
  device: string;
  scans: number;
  percentage: number;
}

export interface ScanByReferrer {
  referrer: string;
  scans: number;
  percentage: number;
}

export interface RecentScan {
  id: string;
  qr_code_id: string;
  scan_time: string;
  ip: string | null;
  user_agent: string | null;
  referrer: string | null;
  lat: number | null;
  lon: number | null;
  city: string | null;
  country: string | null;
  device: string | null;
  os: string | null;
  browser: string | null;
  created_at: string;
}

export interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
}

export interface QRCodesTableProps {
  qrCodes: QRCode[];
  pagination: PaginationInfo;
  onPageChange: (page: number) => void;
  onViewDetails: (qrCodeId: string) => void;
  onDownloadQR: (qrCodeId: string) => void;
}

// Database-specific types
export interface DatabaseQRCode {
  id: string;
  user_id: string | null;
  name: string | null;
  data: string;
  dynamic: number;
  tracking_domain: string | null;
  custom_slug: string | null;
  redirect_url: string | null;
  content_type: string;
  content_data: string | null;
  original_url: string | null;
  email_address: string | null;
  wifi_ssid: string | null;
  created_at: string;
  updated_at: string | null;
}

export interface DatabaseScanAnalytics {
  id: string;
  qr_code_id: string;
  scan_time: string;
  ip: string | null;
  user_agent: string | null;
  referrer: string | null;
  lat: number | null;
  lon: number | null;
  city: string | null;
  country: string | null;
  device: string | null;
  os: string | null;
  browser: string | null;
  created_at: string;
}

// API Response types
export interface DashboardMetricsResponse {
  success: boolean;
  data?: DashboardMetrics;
  error?: string;
}

export interface QRCodesListResponse {
  success: boolean;
  data?: {
    qrCodes: QRCode[];
    pagination: PaginationInfo;
  };
  error?: string;
}

export interface QRAnalyticsResponse {
  success: boolean;
  data?: {
    qrCode: QRCode;
    analytics: QRAnalytics;
  };
  error?: string;
}
