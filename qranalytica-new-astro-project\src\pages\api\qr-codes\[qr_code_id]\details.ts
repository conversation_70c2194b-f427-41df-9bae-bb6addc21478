import type { APIRoute } from 'astro';

export const prerender = false;

export const GET: APIRoute = async ({ params, locals }) => {
  try {
    // @ts-ignore
    const env = locals.runtime.env;
    // @ts-ignore  
    const db = env.DB as D1Database;

    if (!db) {
      return new Response(JSON.stringify({ error: "D1 database not configured." }), { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const { qr_code_id } = params;

    if (!qr_code_id) {
      return new Response(JSON.stringify({ error: "QR code ID is required." }), { 
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get QR code details
    const qrCodeResult = await db.prepare(`
      SELECT 
        id,
        name,
        content_type,
        data,
        original_url,
        email_address,
        wifi_ssid,
        phone_number,
        custom_slug,
        dynamic,
        created_at
      FROM qr_codes 
      WHERE id = ?
    `).bind(qr_code_id).first();

    if (!qrCodeResult) {
      return new Response(JSON.stringify({ error: "QR code not found." }), { 
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get total scans for this QR code
    const totalScansResult = await db.prepare(`
      SELECT COUNT(*) as count 
      FROM qr_code_scan_analytics 
      WHERE qr_code_id = ?
    `).bind(qr_code_id).first();
    const totalScans = totalScansResult?.count || 0;

    // Get recent scans (last 24 hours)
    const recentScansResult = await db.prepare(`
      SELECT COUNT(*) as count 
      FROM qr_code_scan_analytics 
      WHERE qr_code_id = ? AND scan_time >= datetime('now', '-1 day')
    `).bind(qr_code_id).first();
    const recentScans24h = recentScansResult?.count || 0;

    // Get last scan time
    const lastScanResult = await db.prepare(`
      SELECT scan_time 
      FROM qr_code_scan_analytics 
      WHERE qr_code_id = ? 
      ORDER BY scan_time DESC 
      LIMIT 1
    `).bind(qr_code_id).first();
    const lastScanTime = lastScanResult?.scan_time || null;

    const qrCodeDetails = {
      id: qrCodeResult.id,
      name: qrCodeResult.name,
      content_type: qrCodeResult.content_type,
      data: qrCodeResult.data,
      original_url: qrCodeResult.original_url,
      email_address: qrCodeResult.email_address,
      wifi_ssid: qrCodeResult.wifi_ssid,
      phone_number: qrCodeResult.phone_number,
      custom_slug: qrCodeResult.custom_slug,
      dynamic: Boolean(qrCodeResult.dynamic),
      created_at: qrCodeResult.created_at,
      total_scans: totalScans,
      recent_scans_24h: recentScans24h,
      last_scan_time: lastScanTime
    };

    return new Response(JSON.stringify(qrCodeDetails), {
      status: 200,
      headers: { 
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    });

  } catch (error) {
    console.error('QR code details API error:', error);
    
    return new Response(JSON.stringify({ 
      error: error instanceof Error ? error.message : 'Failed to fetch QR code details' 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
