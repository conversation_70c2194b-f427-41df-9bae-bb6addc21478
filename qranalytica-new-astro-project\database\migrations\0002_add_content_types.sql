-- Add content type and structured data support to QR codes
-- Migration: 0002_add_content_types.sql

-- Add new columns to qr_codes table for content type support
ALTER TABLE qr_codes ADD COLUMN content_type TEXT DEFAULT 'url';
ALTER TABLE qr_codes ADD COLUMN content_data TEXT; -- JSON field for structured content data
ALTER TABLE qr_codes ADD COLUMN original_url TEXT; -- Store original URL for URL type
ALTER TABLE qr_codes ADD COLUMN email_address TEXT; -- Store email for email type
ALTER TABLE qr_codes ADD COLUMN wifi_ssid TEXT; -- Store WiFi SSID for wifi type
ALTER TABLE qr_codes ADD COLUMN phone_number TEXT; -- Store phone number for phone type
ALTER TABLE qr_codes ADD COLUMN updated_at DATETIME;

-- Update the updated_at column for existing records
UPDATE qr_codes SET updated_at = created_at WHERE updated_at IS NULL;

-- Create index for content type for faster filtering
CREATE INDEX IF NOT EXISTS idx_qr_codes_content_type ON qr_codes (content_type);

-- Create index for user_id and content_type combination
CREATE INDEX IF NOT EXISTS idx_qr_codes_user_content ON qr_codes (user_id, content_type);

-- Create triggers to handle updated_at timestamp
CREATE TRIGGER IF NOT EXISTS update_qr_codes_updated_at 
    AFTER UPDATE ON qr_codes
    FOR EACH ROW
    BEGIN
        UPDATE qr_codes SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

-- Create trigger to set updated_at on insert if not provided
CREATE TRIGGER IF NOT EXISTS insert_qr_codes_updated_at
    AFTER INSERT ON qr_codes
    FOR EACH ROW
    WHEN NEW.updated_at IS NULL
    BEGIN
        UPDATE qr_codes SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;